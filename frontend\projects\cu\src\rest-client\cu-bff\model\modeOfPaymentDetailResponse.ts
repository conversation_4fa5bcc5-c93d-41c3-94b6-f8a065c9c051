/**
 * BFF Api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { FieldSource } from './fieldSource';


export interface ModeOfPaymentDetailResponse { 
    otherPersonName?: string;
    iban?: string;
    bic?: string;
    /**
     * The date from which the mode of payment is valid (format YYYY-MM-DD)
     */
    validFrom?: string;
    /**
     * Sources for individual fields
     */
    fieldSources?: Array<FieldSource>;
}

