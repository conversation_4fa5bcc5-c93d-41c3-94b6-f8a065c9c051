package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.adapter.out.external.c9.C9Adapter
import be.fgov.onerva.cu.backend.adapter.out.persistence.WaveTaskPersistenceAdapter
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.C9Info
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataPersistCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestTreatedCommand
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.EC1Info
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.port.out.AppConfigurationPort
import be.fgov.onerva.cu.backend.application.port.out.BaremaPort
import be.fgov.onerva.cu.backend.application.port.out.CurrentUserPort
import be.fgov.onerva.cu.backend.application.port.out.FeatureFlagPort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.application.service.helpers.WaveTaskHelper
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class ChangePersonalDataRequestServiceTest {

    @MockK
    private lateinit var loadCitizenPort: LoadCitizenPort

    @MockK
    private lateinit var persistChangePersonalDataPort: PersistChangePersonalDataPort

    @MockK
    private lateinit var waveTaskPort: WaveTaskPort

    @MockK
    private lateinit var waveTaskPersistenceAdapter: WaveTaskPersistenceAdapter

    @MockK
    private lateinit var c9Adapter: C9Adapter

    @MockK
    private lateinit var currentUserPort: CurrentUserPort

    @MockK
    private lateinit var requestInformationPort: RequestInformationPort

    @MockK
    private lateinit var baremaPort: BaremaPort

    @MockK
    private lateinit var waveTaskHelper: WaveTaskHelper

    @MockK
    private lateinit var featureFlagPort: FeatureFlagPort

    @MockK
    private lateinit var appConfigurationPort: AppConfigurationPort

    @InjectMockKs
    private lateinit var service: ChangePersonalDataRequestService

    @Nested
    inner class ReceivedChangePersonalData {

        @Test
        fun `receivedChangePersonalData should process request without C1 information - Feature flag disabled`() {
            // Given
            val requestId = UUID.randomUUID()
            val ssin = "12345678901"

            val command = ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345L,
                c9Type = "400",
                ssin = ssin,
                ec1Id = null,
                introductionDate = LocalDate.now(),
                dateValid = LocalDate.now().plusDays(20),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = null,
                unemploymentOffice = 123,
                scanNumber = 123456789L,
                operatorCode = 123,
                introductionType = null,
                dueDate = LocalDate.now().plusDays(12),
                ec1DisplayUrl = null,
            )

            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns null
            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns false
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            // When
            val result = service.receivedChangePersonalData(command)

            // Then
            assertThat(result).isNull()

            verify(exactly = 0) {
                c9Adapter.loadEC1(any(), any())
                loadCitizenPort.getCitizenNumbox(ssin)
                requestInformationPort.persistRequestInformation(any(), any())
                currentUserPort.getCurrentUsername()

                persistChangePersonalDataPort.persistChangePersonalData(any())
                waveTaskPort.createChangePersonalDataTask(requestId, any())
                waveTaskPersistenceAdapter.persistChangePersonalDataCaptureWaveTask(requestId, any())
                waveTaskHelper.createChangePersonalDataTask(requestId, any())
            }
        }

        @Test
        fun `receivedChangePersonalData should process request without C1 information`() {
            // Given
            val requestId = UUID.randomUUID()
            val ssin = "12345678901"
            val numbox = 42
            val now = LocalDate.now()
            val assignee = "cu_user"

            val command = ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345L,
                c9Type = "400",
                ssin = ssin,
                ec1Id = null,
                introductionDate = now,
                dateValid = now.plusDays(20),
                requestDate = now,
                paymentInstitution = 456,
                entityCode = "EC456",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = null,
                dueDate = now.plusDays(12),
                ec1DisplayUrl = null,
            )

            val persistCommandSlot = slot<ChangePersonalDataPersistCommand>()
            val createChangePersonalDataTaskCommand = slot<CreateChangePersonalDataTaskCommand>()

            val c9Info = C9Info(
                introductionDate = now,
                dateValid = now,
                requestDate = now,
                paymentInstitution = 456,
                entityCode = "EC456",
                unemploymentOffice = 789,
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.PAPER,
                scanNumber = 88776655,
                scanUrl = "http://the-scan-url",
                c9Id = 12345L,
                ssin = ssin,
                type = "400",
                introductionType = null,
                operatorCode = 123,
                ec1Id = null,
                ec1DisplayUrl = null,
                dueDate = now.plusDays(12)
            )

            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns null
            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { c9Adapter.loadC9(12345L) } returns c9Info
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns numbox
            every { currentUserPort.getCurrentUsername() } returns assignee
            every { requestInformationPort.persistRequestInformation(any(), any()) } returns Unit
            every { persistChangePersonalDataPort.persistChangePersonalData(capture(persistCommandSlot)) } returns ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = 12345L,
                c9Type = "400",
                ssin = ssin,
                requestDate = now,
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            every { waveTaskPort.createChangePersonalDataTask(requestId, any()) } returns WaveTask(
                "PROC123",
                "TASK456",
                status = WaveTaskStatus.OPEN,
            )
            justRun { waveTaskPersistenceAdapter.persistChangePersonalDataCaptureWaveTask(any(), any()) }
            every {
                waveTaskHelper.createChangePersonalDataTask(
                    requestId,
                    capture(createChangePersonalDataTaskCommand),
                )
            } returns Unit

            // When
            service.receivedChangePersonalData(command)

            // Then
            assertThat(persistCommandSlot.captured).isNotNull.extracting("c9id", "ssin", "numbox", "dossierId")
                .containsExactly(12345L, ssin, numbox, "OP123SO123")
            assertThat(createChangePersonalDataTaskCommand.captured).isNotNull.extracting(
                "c9id",
                "ssin",
                "numbox",
                "dossierId",
                "paymentInstitution",
                "requestDate",
                "sectOp",
            )
                .containsExactly(
                    12345L, ssin, numbox, "OP123SO123",
                    456, now, "SO123"
                )

            verify(exactly = 1) {
                loadCitizenPort.getCitizenNumbox(ssin)
                persistChangePersonalDataPort.persistChangePersonalData(any())
            }
            verify(exactly = 0) { c9Adapter.loadEC1(any(), any()) }
        }

        @Test
        fun `receivedChangePersonalData should process request without EC1 information`() {
            // Given
            val requestId = UUID.randomUUID()
            val ssin = "12345678901"
            val numbox = 42
            val now = LocalDate.now()
            val assignee = "cu_user"

            val command = ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345L,
                c9Type = "400",
                ssin = ssin,
                ec1Id = null,
                introductionDate = now,
                dateValid = now.plusDays(20),
                requestDate = now,
                paymentInstitution = 456,
                entityCode = "EC456",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = null,
                unemploymentOffice = 789,
                scanNumber = null,
                operatorCode = 123,
                introductionType = null,
                dueDate = now.plusDays(12),
                ec1DisplayUrl = null,
            )
            val c9Info = C9Info(
                introductionDate = now,
                dateValid = now,
                requestDate = now,
                paymentInstitution = 456,
                entityCode = "EC456",
                unemploymentOffice = 789,
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.PAPER,
                scanNumber = null,
                scanUrl = null,
                ssin = ssin,
                c9Id = 12345L,
                type = "400",
                introductionType = null,
                operatorCode = 123,
                ec1Id = null,
                ec1DisplayUrl = null,
                dueDate = now.plusDays(12)
            )

            val persistCommandSlot = slot<ChangePersonalDataPersistCommand>()
            val createChangePersonalDataTaskCommand = slot<CreateChangePersonalDataTaskCommand>()

            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns null

            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { c9Adapter.loadC9(12345L) } returns c9Info
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns numbox
            every { currentUserPort.getCurrentUsername() } returns assignee
            every { requestInformationPort.persistRequestInformation(any(), any()) } returns Unit
            every { persistChangePersonalDataPort.persistChangePersonalData(capture(persistCommandSlot)) } returns ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = 12345L,
                c9Type = "400",
                ssin = ssin,
                requestDate = now,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                documentType = IdentityDocumentType.PAPER,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            every {
                waveTaskHelper.createChangePersonalDataTask(
                    requestId,
                    capture(createChangePersonalDataTaskCommand),
                )
            } returns Unit

            // When
            service.receivedChangePersonalData(command)

            // Then
            assertThat(createChangePersonalDataTaskCommand.captured).isNotNull.extracting(
                "c9id",
                "ssin",
                "numbox",
                "dossierId",
                "paymentInstitution",
                "requestDate",
                "sectOp",
            )
                .containsExactly(
                    12345L, ssin, numbox, "OP123SO123",
                    456, now, "SO123"
                )

            verify(exactly = 1) {
                loadCitizenPort.getCitizenNumbox(ssin)
                persistChangePersonalDataPort.persistChangePersonalData(any())
            }
            verify(exactly = 0) { c9Adapter.loadEC1(any(), any()) }
        }

        @Test
        fun `receivedChangePersonalData should process request with EC1 information`() {
            // Given
            val requestId = UUID.randomUUID()
            val ssin = "12345678901"
            val numbox = 42
            val now = LocalDate.now()
            val ec1Id = 123
            val assignee = "cu_user"

            val command = ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345L,
                c9Type = "400",
                ssin = ssin,
                ec1Id = ec1Id,
                introductionDate = now,
                dateValid = now.plusDays(20),
                requestDate = now,
                paymentInstitution = 456,
                entityCode = "EC456",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = null,
                dueDate = now.plusDays(12),
                ec1DisplayUrl = "http://example.com/ec1",
                )

            val ec1Info = EC1Info(
                citizenInformation = CitizenInformation(
                    firstName = "John",
                    lastName = "Doe",
                    birthDate = now,
                    nationalityCode = 111,
                    address = Address(
                        street = "Test Street",
                        houseNumber = "1",
                        boxNumber = null,
                        city = "Brussels",
                        countryCode = 150,
                        zipCode = "1000",
                        validFrom = LocalDate.of(2022, 1, 1),
                    ),
                ), modeOfPayment = ModeOfPayment(
                    otherPersonName = null, iban = "BE123456789", bic = null, validFrom = LocalDate.of(2025, 1, 1),
                ), unionContribution = UnionContribution(
                    authorized = true, effectiveDate = now,
                )
            )

            val persistCommandSlot = slot<ChangePersonalDataPersistCommand>()
            val createChangePersonalDataTaskCommand = slot<CreateChangePersonalDataTaskCommand>()

            val c9Info = C9Info(
                introductionDate = now,
                dateValid = now,
                requestDate = now,
                paymentInstitution = 456,
                entityCode = "EC456",
                unemploymentOffice = 789,
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.ELECTRONIC,
                scanNumber = null,
                scanUrl = null,
                c9Id = 12345L,
                ssin = ssin,
                type = "400",
                introductionType = null,
                operatorCode = 123,
                ec1Id = ec1Id,
                ec1DisplayUrl = "http://example.com/ec1",
                dueDate = now.plusDays(12)
            )

            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns null

            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { c9Adapter.loadC9(12345L) } returns c9Info
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns numbox
            every { requestInformationPort.persistRequestInformation(any(), any()) } returns Unit
            every { c9Adapter.loadEC1(ec1Id, c9Info.requestDate) } returns ec1Info
            every { currentUserPort.getCurrentUsername() } returns assignee
            every { persistChangePersonalDataPort.persistChangePersonalData(capture(persistCommandSlot)) } returns ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = 12345L,
                c9Type = "400",
                ssin = ssin,
                requestDate = now,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                documentType = IdentityDocumentType.PAPER,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = ec1Id,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            every { waveTaskPort.createChangePersonalDataTask(requestId, any()) } returns WaveTask(
                "PROC123",
                "TASK456",
                status = WaveTaskStatus.OPEN,
            )
            every {
                waveTaskHelper.createChangePersonalDataTask(
                    requestId,
                    capture(createChangePersonalDataTaskCommand),
                )
            } returns Unit

            // When
            service.receivedChangePersonalData(command)

            // Then
            assertThat(persistCommandSlot.captured).extracting(
                "citizenInformation", "modeOfPayment", "unionContribution"
            ).containsExactly(ec1Info.citizenInformation, ec1Info.modeOfPayment, ec1Info.unionContribution)
            assertThat(createChangePersonalDataTaskCommand.captured).isNotNull.extracting(
                "c9id",
                "ssin",
                "numbox",
                "dossierId",
                "paymentInstitution",
                "requestDate",
                "sectOp",
            )
                .containsExactly(
                    12345L, ssin, numbox, "OP123SO123",
                    456, now, "SO123"
                )

            verify(exactly = 1) {
                loadCitizenPort.getCitizenNumbox(ssin)
                c9Adapter.loadEC1(ec1Id, c9Info.requestDate)
                persistChangePersonalDataPort.persistChangePersonalData(any())
            }
        }

        @Test
        fun `receivedChangePersonalData should handle EC1 loading failure`() {
            // Given
            val ssin = "12345678901"
            val ec1Id = 123
            val command = ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345L,
                c9Type = "400",
                ssin = ssin,
                ec1Id = ec1Id,
                introductionDate = LocalDate.now(),
                dateValid = LocalDate.now().plusDays(20),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                scanNumber = 88776655L,
                dueDate = LocalDate.now().plusDays(12),
                operatorCode = 123,
                introductionType = null,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            val assignee = "cu_user"

            val c9Info = C9Info(
                introductionDate = LocalDate.now(),
                dateValid = LocalDate.now(),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                unemploymentOffice = 789,
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.ELECTRONIC,
                scanNumber = null,
                scanUrl = null,
                c9Id = 12345L,
                ssin = ssin,
                type = "400",
                introductionType = null,
                operatorCode = 123,
                ec1Id =     ec1Id,
                ec1DisplayUrl = "http://example.com/ec1",
                dueDate = LocalDate.now().plusDays(12)
            )

            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns null

            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { c9Adapter.loadC9(12345L) } returns c9Info
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns 42
            every { c9Adapter.loadEC1(ec1Id, c9Info.requestDate) } throws RuntimeException("EC1 loading failed")
            every { currentUserPort.getCurrentUsername() } returns assignee

            // When/Then
            assertThatThrownBy { service.receivedChangePersonalData(command) }.isInstanceOf(RuntimeException::class.java)
                .hasMessage("EC1 loading failed")

            verify(exactly = 1) {
                loadCitizenPort.getCitizenNumbox(ssin)
                c9Adapter.loadEC1(ec1Id, c9Info.requestDate)
            }
            verify(exactly = 0) {
                persistChangePersonalDataPort.persistChangePersonalData(any())
                waveTaskPort.createChangePersonalDataTask(any(), any())
                waveTaskPersistenceAdapter.persistChangePersonalDataCaptureWaveTask(any(), any())
            }
        }

        @Test
        fun `receivedChangePersonalData should not process if already exists when allowMultipleC9s is false`() {
            // Given
            val requestId = UUID.randomUUID()
            val ssin = "12345678901"
            val now = LocalDate.now()

            val command = ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345L,
                c9Type = "400",
                ssin = ssin,
                ec1Id = null,
                introductionDate = now,
                dateValid = now.plusDays(20),
                requestDate = now,
                paymentInstitution = 456,
                entityCode = "EC456",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = null,
                dueDate = now.plusDays(12),
                ec1DisplayUrl = null,
            )

            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = 12345L,
                c9Type = "400",
                ssin = ssin,
                requestDate = now,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                documentType = IdentityDocumentType.PAPER,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            // When
            val result = service.receivedChangePersonalData(command)

            // Then
            assertThat(result).isEqualTo(requestId)
        }
    }

    @Nested
    inner class ProcessTreatedChangePersonalData {

        @Test
        fun `should process when validate task exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val c9Id = 12345L
            val ssin = "12345678901"
            val citizenId = 42
            val now = LocalDate.now()
            val processId = "PROC123"
            val captureTaskId = "CAPTURE456"
            val validateTaskId = "VALIDATE789"
            val baremaCode = "ART50"
            val username = "testUser"
            val decisionType = DecisionType.C2Y

            val captureTask = WaveTask(
                processId = processId,
                taskId = captureTaskId,
                status = WaveTaskStatus.CLOSED
            )

            val validateTask = WaveTask(
                processId = processId,
                taskId = validateTaskId,
                status = WaveTaskStatus.OPEN
            )

            val barema = Barema(
                barema = baremaCode,
                article = "Article 50"
            )

            val command = ChangePersonalDataRequestTreatedCommand(
                type = "400",
                ssin = ssin,
                c9Id = c9Id,
                decisionDate = now,
                decisionType = decisionType,
                user = username
            )

            val request = ChangePersonalDataRequest(
                id = requestId,
                c9id = c9Id,
                c9Type = "400",
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = now,
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = captureTask,
                changePersonalDataValidateWaveTask = validateTask,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            // Mocks
            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns null

            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id) } returns request
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns citizenId
            every { baremaPort.getLatestBarema(citizenId, now) } returns barema
            justRun { persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any()) }
            justRun { waveTaskPort.updateChangePersonalDataTaskDecision(processId, validateTaskId, any()) }
            justRun { waveTaskHelper.assignAndCloseValidateDataTask(requestId, validateTaskId, username) }
            justRun { waveTaskPort.closeProcess(processId) }

            // When
            service.processTreatedChangePersonalData(command)

            // Then
            verify(exactly = 1) {
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id)
                loadCitizenPort.getCitizenNumbox(ssin)
                baremaPort.getLatestBarema(citizenId, now)
                persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any())
                waveTaskPort.updateChangePersonalDataTaskDecision(processId, validateTaskId, any())
                waveTaskHelper.assignAndCloseValidateDataTask(requestId, validateTaskId, username)
                waveTaskPort.closeProcess(processId)
            }

            verify(exactly = 0) {
                waveTaskHelper.assignAndCloseDataCaptureTask(any(), any(), username)
            }
        }

        @Test
        fun `should process when only capture task exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val c9Id = 12345L
            val ssin = "12345678901"
            val citizenId = 42
            val now = LocalDate.now()
            val processId = "PROC123"
            val captureTaskId = "CAPTURE456"
            val username = "testUser"
            val decisionType = DecisionType.C2Y

            val captureTask = WaveTask(
                processId = processId,
                taskId = captureTaskId,
                status = WaveTaskStatus.OPEN
            )

            val barema = Barema(
                barema = "ART50",
                article = "Article 50"
            )

            val command = ChangePersonalDataRequestTreatedCommand(
                type = "400",
                ssin = ssin,
                c9Id = c9Id,
                decisionDate = now,
                decisionType = decisionType,
                user = username
            )

            val request = ChangePersonalDataRequest(
                id = requestId,
                c9id = c9Id,
                c9Type = "400",
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = now,
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = captureTask,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            // Mocks
            every { appConfigurationPort.allowMultipleC9s } returns false
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345L) } returns null

            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id) } returns request
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns citizenId
            every { baremaPort.getLatestBarema(citizenId, now) } returns barema
            justRun { persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any()) }
            justRun { waveTaskPort.updateChangePersonalDataTaskDecision(processId, captureTaskId, any()) }
//            justRun { waveTaskPort.assignTaskToUser(captureTaskId, username) }
            every { waveTaskHelper.assignAndCloseDataCaptureTask(requestId, captureTaskId, username) } returns true
            justRun { waveTaskPort.closeProcess(processId) }

            // When
            service.processTreatedChangePersonalData(command)

            // Then
            verify(exactly = 1) {
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id)
                loadCitizenPort.getCitizenNumbox(ssin)
                baremaPort.getLatestBarema(citizenId, now)
                persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any())
                waveTaskPort.updateChangePersonalDataTaskDecision(processId, captureTaskId, any())
//                waveTaskPort.assignTaskToUser(captureTaskId, username)
                waveTaskHelper.assignAndCloseDataCaptureTask(requestId, captureTaskId, username)
                waveTaskPort.closeProcess(processId)
            }

            verify(exactly = 0) {
                waveTaskHelper.assignAndCloseValidateDataTask(any(), any(), username)
//                waveTaskPort.assignTaskToUser(not(eq(captureTaskId)), any())
            }
        }

        @Test
        fun `should process when no barema is found`() {
            // Given
            val requestId = UUID.randomUUID()
            val c9Id = 12345L
            val ssin = "12345678901"
            val citizenId = 42
            val now = LocalDate.now()
            val processId = "PROC123"
            val captureTaskId = "CAPTURE456"
            val username = "testUser"
            val decisionType = DecisionType.C2Y

            val captureTask = WaveTask(
                processId = processId,
                taskId = captureTaskId,
                status = WaveTaskStatus.OPEN
            )

            val command = ChangePersonalDataRequestTreatedCommand(
                type = "400",
                ssin = ssin,
                c9Id = c9Id,
                decisionDate = now,
                decisionType = decisionType,
                user = username
            )

            val request = ChangePersonalDataRequest(
                id = requestId,
                c9id = c9Id,
                c9Type = "400",
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = now,
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = captureTask,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://ec1-display-url",
            )

            // Mocks
            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id) } returns request
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns citizenId
            every { baremaPort.getLatestBarema(citizenId, now) } returns null
            justRun { persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any()) }
            justRun { waveTaskPort.updateChangePersonalDataTaskDecision(processId, captureTaskId, any()) }
//            justRun { waveTaskPort.assignTaskToUser(captureTaskId, username) }
            every { waveTaskHelper.assignAndCloseDataCaptureTask(requestId, captureTaskId, username) } returns true
            justRun { waveTaskPort.closeProcess(processId) }

            // When
            service.processTreatedChangePersonalData(command)

            // Then
            verify(exactly = 1) {
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id)
                loadCitizenPort.getCitizenNumbox(ssin)
                baremaPort.getLatestBarema(citizenId, now)
                persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any())
                waveTaskPort.updateChangePersonalDataTaskDecision(processId, captureTaskId, any())
                waveTaskHelper.assignAndCloseDataCaptureTask(requestId, captureTaskId, username)
                waveTaskPort.closeProcess(processId)
            }
        }

        @Test
        fun `should throw RequestIdNotFoundException when request not found`() {
            // Given
            val c9Id = 12345L
            val ssin = "12345678901"
            val now = LocalDate.now()
            val username = "testUser"
            val decisionType = DecisionType.C2Y

            val command = ChangePersonalDataRequestTreatedCommand(
                type = "400",
                ssin = ssin,
                c9Id = c9Id,
                decisionDate = now,
                decisionType = decisionType,
                user = username
            )

            // Mocks
            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id) } returns null

            // When/Then
            assertThatThrownBy { service.processTreatedChangePersonalData(command) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found for c9id: $c9Id")

            verify(exactly = 1) {
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id)
            }

            verify(exactly = 0) {
                loadCitizenPort.getCitizenNumbox(any())
                baremaPort.getLatestBarema(any(), any())
                persistChangePersonalDataPort.updateChangePersonalDataWithDecision(any(), any())
                waveTaskPort.updateChangePersonalDataTaskDecision(any(), any(), any())
                waveTaskHelper.assignAndCloseDataCaptureTask(any(), any(), username)
                waveTaskHelper.assignAndCloseValidateDataTask(any(), any(), username)
            }
        }

        @Test
        fun `should throw RequestInvalidStateException when no task exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val c9Id = 12345L
            val ssin = "12345678901"
            val now = LocalDate.now()
            val username = "testUser"
            val decisionType = DecisionType.C2Y

            val command = ChangePersonalDataRequestTreatedCommand(
                type = "400",
                ssin = ssin,
                c9Id = c9Id,
                decisionDate = now,
                decisionType = decisionType,
                user = username
            )

            val request = ChangePersonalDataRequest(
                id = requestId,
                c9id = c9Id,
                c9Type = "400",
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = now,
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://ec1-display-url",
            )

            // Mocks
            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id) } returns request

            // When/Then
            assertThatThrownBy { service.processTreatedChangePersonalData(command) }
                .isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("The request does not have a task $requestId")

            verify(exactly = 1) {
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id)
            }

            verify(exactly = 0) {
                loadCitizenPort.getCitizenNumbox(any())
                baremaPort.getLatestBarema(any(), any())
                persistChangePersonalDataPort.updateChangePersonalDataWithDecision(any(), any())
                waveTaskPort.updateChangePersonalDataTaskDecision(any(), any(), any())
                waveTaskPort.assignTaskToUser(any(), any())
                waveTaskHelper.assignAndCloseDataCaptureTask(any(), any(), username)
                waveTaskHelper.assignAndCloseValidateDataTask(any(), any(), username)
            }
        }

        @Test
        fun `should not close tasks when they are already closed`() {
            // Given
            val requestId = UUID.randomUUID()
            val c9Id = 12345L
            val ssin = "12345678901"
            val citizenId = 42
            val now = LocalDate.now()
            val processId = "PROC123"
            val captureTaskId = "CAPTURE456"
            val validateTaskId = "VALIDATE789"
            val baremaCode = "ART50"
            val username = "testUser"
            val decisionType = DecisionType.C2Y

            val captureTask = WaveTask(
                processId = processId,
                taskId = captureTaskId,
                status = WaveTaskStatus.CLOSED
            )

            val validateTask = WaveTask(
                processId = processId,
                taskId = validateTaskId,
                status = WaveTaskStatus.CLOSED
            )

            val barema = Barema(
                barema = baremaCode,
                article = "Article 50"
            )

            val command = ChangePersonalDataRequestTreatedCommand(
                type = "400",
                ssin = ssin,
                c9Id = c9Id,
                decisionDate = now,
                decisionType = decisionType,
                user = username
            )

            val request = ChangePersonalDataRequest(
                id = requestId,
                c9id = c9Id,
                c9Type = "400",
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = now,
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = captureTask,
                changePersonalDataValidateWaveTask = validateTask,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://ec1-display-url",
            )

            // Mocks
            every { featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
            every {
                featureFlagPort.getFeatureListValue(
                    FeatureFlagPort.SUPPORTED_C9_TYPES,
                    emptyList()
                )
            } returns listOf("400", "410")

            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id) } returns request
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns citizenId
            every { baremaPort.getLatestBarema(citizenId, now) } returns barema
            justRun { persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any()) }
            justRun { waveTaskPort.updateChangePersonalDataTaskDecision(processId, validateTaskId, any()) }
            justRun { waveTaskPort.assignTaskToUser(captureTaskId, username) }
            justRun { waveTaskPort.assignTaskToUser(validateTaskId, username) }
            justRun { waveTaskPort.closeProcess(processId) }

            // When
            service.processTreatedChangePersonalData(command)

            // Then
            verify(exactly = 1) {
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(c9Id)
                loadCitizenPort.getCitizenNumbox(ssin)
                baremaPort.getLatestBarema(citizenId, now)
                persistChangePersonalDataPort.updateChangePersonalDataWithDecision(requestId, any())
                waveTaskPort.updateChangePersonalDataTaskDecision(processId, validateTaskId, any())
                waveTaskPort.closeProcess(processId)
            }

            verify(exactly = 0) {
                waveTaskHelper.assignAndCloseDataCaptureTask(any(), any(), username)
                waveTaskHelper.assignAndCloseValidateDataTask(any(), any(), username)
            }
        }
    }
}