package be.fgov.onerva.cu.backend.application.domain

import java.time.LocalDate
import java.util.UUID
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.PastOrPresent
import jakarta.validation.constraints.Size
import be.fgov.onerva.cu.backend.application.validation.ValidModeOfPayment
import be.fgov.onerva.cu.backend.application.validation.ValidNationalityOrCountryCode

/**
 * Represents an attachment/annex in the system with its location and type.
 *
 * @property url The URL where the annex document can be accessed
 * @property type The type of the annex, indicating whether it's an EC1 form or scanned documents
 *
 * @see AnnexType
 */
data class Annex(
    val url: String,
    val type: AnnexType,
)

/**
 * Basic information about a request in the system.
 *
 * @property requestDate The date when the request was made
 * @property ssin Social Security Identification Number of the requester
 * @property firstName First name of the requester
 * @property lastName Last name of the requester
 * @property annexes List of annexes attached to the request
 */
data class RequestBasicInfo(
    val requestDate: LocalDate,
    val ssin: String,
    val firstName: String,
    val lastName: String,
    val introductionDate: LocalDate,
    val dateValid: LocalDate?,
    val annexes: List<Annex>,
    val decisionType: DecisionType?,
    val decisionBarema: String?,
    val c9Id: Long,
    val pushbackStatus: SyncFollowUpStatus?,
    val documentType: IdentityDocumentType,
)

/**
 * Abstract base class for all request types in the system.
 *
 * @property id Unique identifier for the request
 * @property c9id Associated C9 form identifier
 * @property c9Type The type of the c9
 * @property ssin Social Security Identification Number of the requester
 * @property opKey Operation key
 * @property sectOp Section operator
 * @property requestDate The date when the request was made
 */
abstract class Request(
    open val id: UUID,
    open val c9id: Long,
    open val c9Type: String,
    open val ssin: String,
    open val opKey: String,
    open val sectOp: String,
    open val requestDate: LocalDate,
    open val decisionType: DecisionType?,
    open val decisionBarema: String?,
    open val numbox: Int?,
    open val introductionDate: LocalDate?,
    open val paymentInstitution: Int?,
    open val entityCode: String?,
    open val dossierId: String?,
    open val documentType: IdentityDocumentType,
    open val scanUrl: String?,
    open val unemploymentOffice: Int?,
    open val dateValid: LocalDate?,
    open val scanNumber: Long?,
    open val operatorCode: Int?,
    open val introductionType: IntroductionType?,
    open val dueDate: LocalDate?,
    open val ec1Id: Int?,
    open val ec1DisplayUrl: String?,
) {
    // Common methods for all request types can be placed here

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is Request) return false

        return id == other.id &&
                c9id == other.c9id &&
                ssin == other.ssin &&
                opKey == other.opKey &&
                sectOp == other.sectOp &&
                requestDate == other.requestDate
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + c9id.hashCode()
        result = 31 * result + ssin.hashCode()
        result = 31 * result + opKey.hashCode()
        result = 31 * result + sectOp.hashCode()
        result = 31 * result + requestDate.hashCode()
        return result
    }

    override fun toString(): String {
        return "Request(id=$id, c9id=$c9id, ssin='$ssin', opKey='$opKey', sectOp='$sectOp', requestDate=$requestDate)"
    }
}

/**
 * Basic request implementation that can be used directly
 */
data class BasicRequest(
    override val id: UUID,
    override val c9id: Long,
    override val c9Type: String,
    override val ssin: String,
    override val opKey: String,
    override val sectOp: String,
    override val requestDate: LocalDate,
    override val decisionType: DecisionType?,
    override val decisionBarema: String?,
    override val numbox: Int?,
    override val introductionDate: LocalDate?,
    override val paymentInstitution: Int?,
    override val entityCode: String?,
    override val dossierId: String?,
    override val documentType: IdentityDocumentType,
    override val scanUrl: String?,
    override val unemploymentOffice: Int?,
    override val dateValid: LocalDate?,
    override val scanNumber: Long?,
    override val operatorCode: Int?,
    override val introductionType: IntroductionType?,
    override val dueDate: LocalDate?,
    override val ec1Id: Int?,
    override val ec1DisplayUrl: String?,
) : Request(
    id,
    c9id,
    c9Type,
    ssin,
    opKey,
    sectOp,
    requestDate,
    decisionType,
    decisionBarema,
    numbox,
    introductionDate,
    paymentInstitution,
    entityCode,
    dossierId,
    documentType,
    scanUrl,
    unemploymentOffice,
    dateValid,
    scanNumber,
    operatorCode,
    introductionType,
    dueDate,
    ec1Id,
    ec1DisplayUrl,
)

/**
 * Represents a change of address request in the system, extending the base Request class.
 *
 * @property documentType The document type (Electronic or Paper)
 * @property citizenInformation Citizen personal information
 * @property modeOfPayment Payment method details
 * @property unionContribution Union contribution information
 * @property changePersonalDataCaptureWaveTask Wave task for data capture
 * @property changePersonalDataValidateWaveTask Wave task for data validation
 */
data class ChangePersonalDataRequest(
    override val id: UUID,
    override val c9Type: String,
    override val c9id: Long,
    override val ssin: String,
    override val opKey: String,
    override val sectOp: String,
    override val requestDate: LocalDate,
    override val decisionType: DecisionType?,
    override val decisionBarema: String?,
    override val numbox: Int?,
    override val documentType: IdentityDocumentType,
    override val paymentInstitution: Int?,
    override val entityCode: String?,
    override val dossierId: String?,
    override val introductionDate: LocalDate?,
    override val scanUrl: String?,
    override val unemploymentOffice: Int?,
    override val dateValid: LocalDate?,
    override val scanNumber: Long?,
    override val operatorCode: Int?,
    override val introductionType: IntroductionType?,
    override val dueDate: LocalDate?,
    override val ec1Id: Int?,
    override val ec1DisplayUrl: String?,
    val citizenInformation: CitizenInformation?,
    val modeOfPayment: ModeOfPayment?,
    val unionContribution: UnionContribution?,
    val requestInformation: RequestInformation?,
    var changePersonalDataCaptureWaveTask: WaveTask?,
    var changePersonalDataValidateWaveTask: WaveTask?,
    val status: RequestStatus = RequestStatus.OPEN,
    val processInWave: Boolean = false,
    val routingDecisions: Set<RoutingDecisionItem> = emptySet(),
) : Request(
    id,
    c9id,
    c9Type,
    ssin,
    opKey,
    sectOp,
    requestDate,
    decisionType,
    decisionBarema,
    numbox,
    introductionDate,
    paymentInstitution,
    entityCode,
    dossierId,
    documentType,
    scanUrl,
    unemploymentOffice,
    dateValid,
    scanNumber,
    operatorCode,
    introductionType,
    dueDate,
    ec1Id,
    ec1DisplayUrl,
) {
    /**
     * Determines if this request can be processed in Wave based on routing decisions.
     * Returns true ONLY if ALL manual verification types are answered (non-null) AND all answers match expected values.
     * Returns false in all other cases (incomplete answers, wrong answers, or no answers).
     */
    fun canBeProcessedInWave(): Boolean {
        // If no routing decisions exist, cannot be processed in Wave
        if (routingDecisions.isEmpty()) return false
        
        // Get all manual verification types that need to be answered
        val allRequiredTypes = ManualVerificationType.entries.toSet()
        val answeredTypes = routingDecisions.map { it.type }.toSet()
        
        // All types must be present
        if (answeredTypes != allRequiredTypes) return false
        
        // All values must be non-null (answered) and match expected values for Wave processing
        return routingDecisions.all { decision ->
            decision.value != null && decision.type.allowsWaveProcessing(decision.value!!)
        }
    }
}

/**
 * Represents a physical address.
 *
 * @property street Street name
 * @property houseNumber House number
 * @property boxNumber Optional box or apartment number
 * @property countryCode Country name
 * @property city City name
 * @property zipCode Postal code
 */
data class Address(
    @field:NotBlank(message = "Street is required")
    @field:Size(max = 255)
    val street: String,
    @field:NotBlank(message = "Number is required")
    @field:Size(max = 11)
    val houseNumber: String,
    val boxNumber: String? = null,
    @field:ValidNationalityOrCountryCode
    var countryCode: Int,
    @field:NotBlank(message = "City is required")
    @field:Size(max = 255)
    var city: String,
    @field:NotBlank(message = "Zip Code is required")
    @field:Size(max = 10)
    var zipCode: String,
    var validFrom: LocalDate,
)

/**
 * Contains citizen personal and address information.
 *
 * @property firstName Citizen first name
 * @property lastName Citizen last name
 * @property birthDate Citizen date of birth
 * @property nationalityCode Citizen nationality
 * @property address Citizen physical address
 */
data class CitizenInformation(
    val firstName: String,
    val lastName: String,
    val birthDate: LocalDate,
    val nationalityCode: Int,
    val address: Address,
)

data class UpdateCitizenInformation(
    @field:NotNull(message = "Birth date is required")
    @field:PastOrPresent(message = "Birth date must be in the past or present")
    val birthDate: LocalDate,
    @field:NotNull(message = "Nationality is required")
    @field:ValidNationalityOrCountryCode
    val nationalityCode: Int,
    @field:NotNull(message = "Address is required")
    @field:Valid
    val address: Address,
)

/**
 * Represents payment method details for an employee.
 *
 * @property otherPersonName Name of the account holder if not the employee
 * @property iban International Bank Account Number
 * @property bic Optional Bank Identifier Code (SWIFT code)
 * @property validFrom Date from which the payment details are valid
 */
data class ModeOfPayment(
    @field:Size(max = 255)
    val otherPersonName: String?,
    val iban: String,
    val bic: String?,
    val validFrom: LocalDate,
)

@ValidModeOfPayment
data class UpdateModeOfPayment(
    val otherPersonName: String?,
    val iban: String,
    val bic: String?,
    val validFrom: LocalDate,
)

/**
 * Information about an employee's union contribution status.
 *
 * @property authorized Indicates if union contribution is authorized
 * @property effectiveDate Date from which the contribution status is effective
 * @property source Source of the information (C1, ONEM, Authentic Sources)
 */
data class UnionContribution(
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

data class UpdateUnionContribution(
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

/**
 * Represents consolidated EC1 form information containing employee details, payment information, and union contribution data.
 *
 * @property citizenInformation Personal and address information of the employee
 * @property modeOfPayment Payment preferences and bank account details
 * @property unionContribution Union membership and contribution details
 */
data class EC1Info(
    val citizenInformation: CitizenInformation?,
    val modeOfPayment: ModeOfPayment?,
    val unionContribution: UnionContribution?,
)

/**
 * Represents the C9 information as extracted by the C9Adapter for use by the Service layer in the
 * application.
 * @property c9Id The id of the c9
 * @property type The type of the c9
 * @property sectOp Section operator identifier
 * @property opKey Operation key for tracking
 * @property requestDate Date when the citizen submitted the request
 * @property paymentInstitution Identifier of the payment institution handling the case
 * @property entityCode Optional code identifying the processing entity
 * @property unemploymentOffice Optional identifier of the unemployment office
 * @property documentType Type of identity document used for the request (Electronic or Paper)
 * @property ec1Id Optional identifier of the EC1 form
 * @property ec1DisplayUrl Optional URL to display the EC1 form
 */
data class C9Info(
    val c9Id: Long,
    val type: String,
    val scanUrl: String?,
    val ssin: String,
    val sectOp: String,
    val opKey: String,
    val introductionDate: LocalDate,
    val dateValid: LocalDate?,
    val requestDate: LocalDate,
    val paymentInstitution: Int,
    val entityCode: String,
    val unemploymentOffice: Int?,
    val scanNumber: Long?,
    val documentType: IdentityDocumentType,
    val ec1Id: Int?,
    val ec1DisplayUrl: String?,
    val introductionType: IntroductionType?,
    val operatorCode: Int?,
    val dueDate: LocalDate?,
)

data class AttestRefInfo(
    val id: Int,
    val displayUrl: String,
)

/**
 * Represents a task in the Wave system.
 *
 * @property processId Identifier of the Wave process
 * @property taskId Identifier of the specific task
 * @property waveTaskStatus Status of the task
 */
data class WaveTask(
    val processId: String,
    val taskId: String,
    val status: WaveTaskStatus,
) {
    val waveUrl: String
        get() = "/processes-page/process/(task-detail/$processId!!sidemenu:task-detail/$taskId)"
}

/**
 * Represents citizen information in the system.
 *
 * This data class contains basic identification and contact information for a citizen.
 * It is typically used when retrieving citizen data from external systems through the
 * [LoadCitizenPort] interface.
 *
 * @property firstName The citizen's first name
 * @property lastName The citizen's last name
 * @property numbox The unique numeric identifier used for citizen
 * @property zipCode The postal code of the citizen's residence
 *
 * @see be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
 */
data class Citizen(
    val firstName: String,
    val lastName: String,
    val numbox: Int,
    val zipCode: String,
)

/**
 * Request status indicating whether a request is open for modifications or closed.
 * OPEN: Request can be modified, routing decisions can be changed
 * CLOSED: Request is finalized, routing decisions are immutable
 */
enum class RequestStatus {
    OPEN,
    CLOSED
}

/**
 * Manual verification types with their expected values for Wave processing.
 * All verification types expect 'false' answers to allow Wave processing.
 * @property expectedValueForWaveProcessing The expected answer (false) that allows processing in Wave
 */
enum class ManualVerificationType(val expectedValueForWaveProcessing: Boolean) {
    CITIZEN_OVER_65_YEARS_OLD(false),
    RELEVANT_TO_PORT_WORKER(false),
    NON_BELGIAN_RESIDENT(false),
    TRANSFER_BETWEEN_OP_OR_OC(false),
    REQUEST_FOR_ECONOMIC_REASON(false),
    RELEVANT_TO_APPRENTICESHIP(false),
    CASE_OF_IMPULSION(false);

    /**
     * Determines if the given answer allows Wave processing for this verification type.
     * @param answer The user's answer to the verification question
     * @return true if this answer allows Wave processing, false otherwise
     */
    fun allowsWaveProcessing(answer: Boolean): Boolean = answer == expectedValueForWaveProcessing
}

data class RoutingDecisionItem(val type: ManualVerificationType, val value: Boolean?) {
    /**
     * Computed property that determines if this decision allows Wave processing.
     * Returns true only if value is non-null and matches the expected value.
     * Returns false if value is null (unanswered) or doesn't match expected value.
     */
    val canBeProcessedInWave: Boolean get() = value?.let { type.allowsWaveProcessing(it) } ?: false
}

/**
 * Represents the complete routing decisions response containing both the process in wave status
 * and the individual routing decisions.
 * 
 * @property processInWave Whether the request can be processed in Wave (nullable if not determined)
 * @property routingDecisions Set of routing decision items for manual verification
 */
data class RoutingDecision(
    val processInWave: Boolean?,
    val routingDecisions: Set<RoutingDecisionItem>
)

/**
 * Represents basic request information in the system.
 * @property requestDate The date when the request was made
 */
data class RequestInformation(
    val requestDate: LocalDate,
)

/**
 * Represents the result of a retrieval operation for snapshot data.
 *
 * This sealed class provides a type-safe way to handle different states of data retrieval:
 * - When data is found, it includes the value and whether it's read-only
 * - When data is not found, it provides a specific type to handle this case
 *
 * @param T The type of data being retrieved
 */
sealed class Snapshot<out T> {
    /**
     * Represents a successful data retrieval operation.
     *
     * @property value The retrieved data
     * @property readonly Indicates if the data is read-only and cannot be modified
     */
    data class Found<T>(val value: T, val readonly: Boolean) : Snapshot<T>()

    /**
     * Represents a case where the requested data was not found (it does not exist).
     */
    data object NotFound : Snapshot<Nothing>()
}

data class WaveTaskRevisionNumbers(
    val citizenInformationRevisionNumber: Int?,
    val modeOfPaymentRevisionNumber: Int?,
    val unionContributionRevisionNumber: Int?,
    val requestInformationRevisionNumber: Int?,
)

data class Barema(val barema: String, val article: String?)
