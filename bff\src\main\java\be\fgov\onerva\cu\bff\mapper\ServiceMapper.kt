package be.fgov.onerva.cu.bff.mapper

import be.fgov.onerva.cu.bff.rest.server.priv.model.Address
import be.fgov.onerva.cu.bff.rest.server.priv.model.AddressNullable
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailNullableResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailResponse

fun Address.toAddressNullable() = AddressNullable(
    boxNumber = this.boxNumber,
    city = this.city,
    countryCode = this.countryCode,
    houseNumber = this.houseNumber,
    street = this.street,
    zipCode = this.zipCode,
    validFrom = this.validFrom,
)

fun CitizenInformationDetailResponse.toCitizenInformationDetailNullableResponse() =
    CitizenInformationDetailNullableResponse(
        birthDate = this.birthDate,
        nationalityCode = this.nationalityCode,
        address = this.address?.toAddressNullable(),
        fieldSources = this.fieldSources,
    )