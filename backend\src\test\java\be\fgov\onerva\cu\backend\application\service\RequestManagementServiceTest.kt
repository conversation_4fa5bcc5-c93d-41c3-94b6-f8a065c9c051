package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.AnnexType
import be.fgov.onerva.cu.backend.application.domain.C9Info
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.Citizen
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.EC1Info
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.SyncFollowUpStatus
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.C9Port
import be.fgov.onerva.cu.backend.application.port.out.CurrentUserPort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.backend.application.port.out.SnapshotPort
import be.fgov.onerva.cu.backend.application.port.out.SyncFollowUpPort
import be.fgov.onerva.cu.backend.application.port.out.UpdateCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.application.service.helpers.WaveTaskHelper
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import io.mockk.verifyOrder

@ExtendWith(MockKExtension::class)
class RequestManagementServiceTest {

    @MockK
    private lateinit var persistChangePersonalDataPort: PersistChangePersonalDataPort

    @MockK
    private lateinit var loadCitizenPort: LoadCitizenPort

    @MockK
    private lateinit var updateCitizenPort: UpdateCitizenPort

    @MockK
    private lateinit var c9Port: C9Port

    @MockK
    private lateinit var currentUserPort: CurrentUserPort

    @MockK
    private lateinit var waveTaskHelper: WaveTaskHelper

    @MockK
    private lateinit var waveTaskPort: WaveTaskPort

    @MockK
    private lateinit var syncFollowUpPort: SyncFollowUpPort

    @MockK
    private lateinit var snapshotPort: SnapshotPort

    @InjectMockKs
    private lateinit var requestManagementService: RequestManagementService

    @Nested
    @DisplayName("getRequestBasicInfo")
    inner class GetRequestBasicInfo {
        @Test
        fun `should return request basic info when request exists`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val requestDate = LocalDate.of(2024, 12, 16)
            val ssin = "123456789"

            val c9id = 12345L
            val ec1Id = 1234
            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = c9id,
                c9Type = "400",
                requestDate = requestDate,
                ssin = ssin,
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = ec1Id,
                ec1DisplayUrl = "http://ec1-display-url",
            )
            val c9Info = C9Info(
                c9Id = 12345,
                type = "400",
                scanUrl = "http://the-scan-url",
                ssin = ssin,
                sectOp = "456",
                opKey = "987",
                introductionDate = LocalDate.of(2024, 12, 15),
                dateValid = LocalDate.of(2024, 12, 15),
                requestDate = requestDate,
                paymentInstitution = 123,
                entityCode = "876",
                scanNumber = 123456L,
                documentType = IdentityDocumentType.ELECTRONIC,
                unemploymentOffice = 789,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                operatorCode = 123,
                ec1Id = ec1Id,
                ec1DisplayUrl = "http://the-ec1-url",
                dueDate = LocalDate.of(2024, 12, 23),
            )
            val citizen = Citizen(
                firstName = "John", lastName = "Doe", numbox = 42, zipCode = "1000"
            )

            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns changePersonalDataRequest
            every { loadCitizenPort.getCitizenBySsin(ssin) } returns citizen
            every { c9Port.loadC9(c9id) } returns c9Info
            every { c9Port.loadEC1(ec1Id, requestDate) } returns EC1Info(
                citizenInformation = CitizenInformation(
                    firstName = "John",
                    lastName = "Doe",
                    birthDate = LocalDate.of(1990, 1, 1),
                    nationalityCode = 111,
                    address = Address(
                        street = "Main Street",
                        houseNumber = "42",
                        boxNumber = "A",
                        zipCode = "1000",
                        city = "Brussels",
                        countryCode = 150,
                        validFrom = LocalDate.of(2022, 1, 1),
                    ),
                ),
                modeOfPayment = null,
                unionContribution = null,
            )
            every { syncFollowUpPort.getSyncFollowUpStatusByRequestId(requestId) } returns SyncFollowUpStatus.OK

            // When
            val result = requestManagementService.getRequestBasicInfo(requestId)

            // Then
            assertThat(result).isNotNull.extracting("requestDate", "ssin", "firstName", "lastName", "pushbackStatus")
                .containsExactly(requestDate, ssin, "John", "Doe", SyncFollowUpStatus.OK)

            verify(exactly = 1) { persistChangePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { loadCitizenPort.getCitizenBySsin(ssin) }
        }

        @Test
        fun `should load C9 data when payment institution is missing`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val requestDate = LocalDate.of(2024, 12, 16)
            val ssin = "123456789"
            val c9id = 12345L

            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = c9id,
                c9Type = "400",
                requestDate = requestDate,
                ssin = ssin,
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = null, // Missing payment institution
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = null,
                ec1DisplayUrl = null,
            )

            val c9Info = C9Info(
                c9Id = 12345,
                type = "400",
                scanUrl = "http://the-scan-url",
                ssin = ssin,
                sectOp = "456",
                opKey = "987",
                introductionDate = LocalDate.of(2024, 12, 15),
                dateValid = LocalDate.of(2024, 12, 15),
                requestDate = LocalDate.of(2024, 12, 15),
                paymentInstitution = 123,
                entityCode = "876",
                scanNumber = 123456L,
                documentType = IdentityDocumentType.ELECTRONIC,
                unemploymentOffice = 789,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                operatorCode = 123,
                ec1Id = null,
                ec1DisplayUrl = null,
                dueDate = LocalDate.of(2024, 12, 23)
            )

            val citizen = Citizen(
                firstName = "John", lastName = "Doe", numbox = 42, zipCode = "1000"
            )

            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns changePersonalDataRequest
            every { c9Port.loadC9(c9id) } returns c9Info
            every { persistChangePersonalDataPort.updateChangePersonalDataWithC9EnvelopeData(requestId, any()) } returns Unit
            every { loadCitizenPort.getCitizenBySsin(ssin) } returns citizen
            every { syncFollowUpPort.getSyncFollowUpStatusByRequestId(requestId) } returns SyncFollowUpStatus.OK

            // When
            val result = requestManagementService.getRequestBasicInfo(requestId)

            // Then
            assertThat(result).isNotNull.extracting("requestDate", "ssin", "firstName", "lastName", "pushbackStatus")
                .containsExactly(requestDate, ssin, "John", "Doe", SyncFollowUpStatus.OK)

            verify(exactly = 1) { 
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                c9Port.loadC9(c9id)
                persistChangePersonalDataPort.updateChangePersonalDataWithC9EnvelopeData(requestId, any())
                loadCitizenPort.getCitizenBySsin(ssin)
            }
            verify(exactly = 0) { c9Port.loadEC1(any(), any()) }
        }

        @Test
        fun `should create annexes when ec1Id and scanUrl are present`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val requestDate = LocalDate.of(2024, 12, 16)
            val ssin = "123456789"
            val c9id = 12345L
            val ec1Id = 1234

            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = c9id,
                c9Type = "400",
                requestDate = requestDate,
                ssin = ssin,
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = ec1Id,
                ec1DisplayUrl = "http://ec1-display-url",
            )

            val citizen = Citizen(
                firstName = "Jane", lastName = "Smith", numbox = 42, zipCode = "1000"
            )

            val ec1Info = EC1Info(
                citizenInformation = null, // No citizen information in EC1
                modeOfPayment = null,
                unionContribution = null,
            )

            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns changePersonalDataRequest
            every { loadCitizenPort.getCitizenBySsin(ssin) } returns citizen
            every { c9Port.loadEC1(ec1Id, requestDate) } returns ec1Info
            every { syncFollowUpPort.getSyncFollowUpStatusByRequestId(requestId) } returns SyncFollowUpStatus.OK

            // When
            val result = requestManagementService.getRequestBasicInfo(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("Jane")
            assertThat(result.lastName).isEqualTo("Smith")
            assertThat(result.annexes).hasSize(2)
            assertThat(result.annexes).extracting("type").containsExactlyInAnyOrder(AnnexType.EC1, AnnexType.SCANNED_DOCUMENTS)

            verify(exactly = 1) { 
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                c9Port.loadEC1(ec1Id, requestDate)
                loadCitizenPort.getCitizenBySsin(ssin)
            }
        }

        @Test
        fun `should create only scanned documents annex when ec1Id is null`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val requestDate = LocalDate.of(2024, 12, 16)
            val ssin = "123456789"
            val c9id = 12345L

            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = c9id,
                c9Type = "400",
                requestDate = requestDate,
                ssin = ssin,
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = "http://the-scan-url",
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = null, // No EC1
                ec1DisplayUrl = null,
            )

            val citizen = Citizen(
                firstName = "Bob", lastName = "Johnson", numbox = 42, zipCode = "1000"
            )

            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns changePersonalDataRequest
            every { loadCitizenPort.getCitizenBySsin(ssin) } returns citizen
            every { syncFollowUpPort.getSyncFollowUpStatusByRequestId(requestId) } returns SyncFollowUpStatus.OK

            // When
            val result = requestManagementService.getRequestBasicInfo(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("Bob")
            assertThat(result.lastName).isEqualTo("Johnson")
            assertThat(result.annexes).hasSize(1)
            assertThat(result.annexes).extracting("type").containsExactly(AnnexType.SCANNED_DOCUMENTS)

            verify(exactly = 1) { 
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                loadCitizenPort.getCitizenBySsin(ssin)
            }
            verify(exactly = 0) { c9Port.loadEC1(any(), any()) }
        }

        @Test
        fun `should create no annexes when both ec1Id and scanUrl are null`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val requestDate = LocalDate.of(2024, 12, 16)
            val ssin = "123456789"
            val c9id = 12345L

            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                opKey = "OP123",
                sectOp = "SO123",
                c9id = c9id,
                c9Type = "400",
                requestDate = requestDate,
                ssin = ssin,
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
                numbox = 1234,
                paymentInstitution = 456,
                entityCode = "12",
                dossierId = "OP123SO123",
                introductionDate = LocalDate.now(),
                scanUrl = null, // No scan URL
                unemploymentOffice = 789,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 88776655L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = null, // No EC1
                ec1DisplayUrl = null,
            )

            val citizen = Citizen(
                firstName = "Alice", lastName = "Williams", numbox = 42, zipCode = "1000"
            )

            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns changePersonalDataRequest
            every { loadCitizenPort.getCitizenBySsin(ssin) } returns citizen
            every { syncFollowUpPort.getSyncFollowUpStatusByRequestId(requestId) } returns SyncFollowUpStatus.OK

            // When
            val result = requestManagementService.getRequestBasicInfo(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("Alice")
            assertThat(result.lastName).isEqualTo("Williams")
            assertThat(result.annexes).isEmpty()

            verify(exactly = 1) { 
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                loadCitizenPort.getCitizenBySsin(ssin)
            }
            verify(exactly = 0) { c9Port.loadEC1(any(), any()) }
        }

        @Test
        fun `should throw RequestIdNotFoundException when request does not exist`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")

            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns null

            // When/Then
            assertThatThrownBy { requestManagementService.getRequestBasicInfo(requestId) }.isInstanceOf(
                RequestIdNotFoundException::class.java
            ).hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { persistChangePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { loadCitizenPort.getCitizenBySsin(any()) }
        }
    }

    @Nested
    inner class CloseTaskForRequestAndCreateNextTests {
        private val requestId = UUID.randomUUID()
        private val currentUser = "testUser"
        private val processId = "process-123"
        private val taskId = "task-456"
        private val c9Id = 12345L
        private val ssin = "123456789"
        private val numbox = 42
        private val entityCode = "ENTITY-123"
        private val sectOp = "SO123"
        private val opKey = "OP456"
        private val requestDate = LocalDate.now()
        private val receptionDate = LocalDate.now().minusDays(1)

        private val mockC9Info = C9Info(
            c9Id = c9Id,
            type = "400",
            scanUrl = null,
            ssin = ssin,
            sectOp = sectOp,
            opKey = opKey,
            introductionDate = requestDate,
            dateValid = null,
            requestDate = requestDate,
            paymentInstitution = 456,
            entityCode = entityCode,
            unemploymentOffice = 789,
            scanNumber = null,
            documentType = IdentityDocumentType.ELECTRONIC,
            introductionType = IntroductionType.INTRO_FIRST_DEMAND,
            operatorCode = 123,
            ec1Id = null,
            ec1DisplayUrl = null,
            dueDate = requestDate.plusDays(12)
        )

        private val mockTask = WaveTask(
            processId = processId,
            taskId = taskId,
            status = WaveTaskStatus.OPEN
        )

        private val mockRequestWithCitizenInfoAndModeOfPayment = ChangePersonalDataRequest(
            id = requestId,
            c9id = c9Id,
            c9Type = "400",
            ssin = ssin,
            opKey = opKey,
            sectOp = sectOp,
            requestDate = requestDate,
            decisionType = null,
            decisionBarema = null,
            documentType = IdentityDocumentType.ELECTRONIC,
            citizenInformation = CitizenInformation(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = be.fgov.onerva.cu.backend.application.domain.Address(
                    street = "Test Street",
                    houseNumber = "1",
                    boxNumber = null,
                    countryCode = 150,
                    city = "Brussels",
                    zipCode = "1000",
                    validFrom = LocalDate.of(2022, 1, 1),
                )
            ),
            modeOfPayment = ModeOfPayment(
                otherPersonName = null,
                iban = "BE123456789",
                bic = null,
                validFrom = LocalDate.of(2025, 1, 1)
            ),
            unionContribution = null,
            requestInformation = null,
            changePersonalDataCaptureWaveTask = mockTask,
            changePersonalDataValidateWaveTask = mockTask,
            numbox = 1234,
            paymentInstitution = 456,
            entityCode = "12",
            dossierId = "OP123SO123",
            introductionDate = LocalDate.now(),
            scanUrl = "http://the-scan-url",
            unemploymentOffice = 789,
            dateValid = LocalDate.now().plusDays(20),
            scanNumber = 88776655L,
            operatorCode = 123,
            introductionType = IntroductionType.INTRO_FIRST_DEMAND,
            dueDate = LocalDate.now().plusDays(12),
            ec1Id = 1234,
            ec1DisplayUrl = "http://ec1-display-url",
        )

        private val commandSlot = slot<CreateChangePersonalDataTaskCommand>()

        private val mockUnionContribution = UnionContribution(
            authorized = true,
            effectiveDate = LocalDate.now()
        )

        private val mockRequestWithUnionContribution = mockRequestWithCitizenInfoAndModeOfPayment.copy(
            unionContribution = mockUnionContribution,
            changePersonalDataValidateWaveTask = mockTask
        )

        private fun setupBaseMocks() {
            every { currentUserPort.getCurrentUsername() } returns currentUser
            every { c9Port.loadC9(c9Id) } returns mockC9Info
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns numbox
            every { waveTaskPort.assignTaskToUser(taskId, currentUser) } returns Unit
        }

        @Test
        fun `should throw RequestIdNotFoundException when request is not found`() {
            // Given
            val nonExistentRequestId = UUID.randomUUID()
            val taskCode = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE

            every { currentUserPort.getCurrentUsername() } returns currentUser
            every { persistChangePersonalDataPort.getChangePersonalDataById(nonExistentRequestId) } returns null

            // When/Then
            assertThatThrownBy {
                requestManagementService.closeTaskForRequestAndCreateNext(nonExistentRequestId, taskCode)
            }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessageContaining("Request ID not found")
        }

        @Test
        fun `should throw WaveTaskNotFoundException when task code is invalid`() {
            // Given
            val invalidTaskCode = "INVALID_TASK_CODE"

            every { currentUserPort.getCurrentUsername() } returns currentUser
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithCitizenInfoAndModeOfPayment

            // When/Then
            assertThatThrownBy {
                requestManagementService.closeTaskForRequestAndCreateNext(requestId, invalidTaskCode)
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessageContaining("Task not found")
        }

        @Test
        fun `should throw WaveTaskNotFoundException when task is null`() {
            // Given
            val taskCode = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
            val requestWithNullTask = mockRequestWithCitizenInfoAndModeOfPayment.copy(
                changePersonalDataCaptureWaveTask = null
            )

            every { currentUserPort.getCurrentUsername() } returns currentUser
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns requestWithNullTask

            // When/Then
            assertThatThrownBy {
                requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessageContaining("Task not found")
        }

        @Test
        fun `should throw RequestInvalidStateException when citizenInformation is null for data capture task`() {
            // Given
            val taskCode = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
            val requestWithNullCitizenInfo = mockRequestWithCitizenInfoAndModeOfPayment.copy(
                citizenInformation = null
            )

            setupBaseMocks()
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns requestWithNullCitizenInfo
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns mockRequestWithCitizenInfoAndModeOfPayment

            // When/Then
            assertThatThrownBy {
                requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)
            }
                .isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessageContaining("Citizen information is not filled in")
        }

        @Test
        fun `should throw RequestInvalidStateException when modeOfPayment is null for data capture task`() {
            // Given
            val taskCode = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
            val requestWithNullModeOfPayment = mockRequestWithCitizenInfoAndModeOfPayment.copy(
                modeOfPayment = null
            )

            setupBaseMocks()
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns requestWithNullModeOfPayment
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns mockRequestWithCitizenInfoAndModeOfPayment

            // When/Then
            assertThatThrownBy {
                requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)
            }
                .isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessageContaining("Mode of payment is not filled in")
        }

        @Test
        fun `should close data capture task and create validation task when successful`() {
            // Given
            val taskCode = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
            val newValidationTask = WaveTask(
                processId = "new-process-123",
                taskId = "new-task-456",
                status = WaveTaskStatus.OPEN
            )

            setupBaseMocks()
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithCitizenInfoAndModeOfPayment
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns mockRequestWithCitizenInfoAndModeOfPayment
            every { waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, currentUser) } returns true
            every {
                waveTaskHelper.createChangePersonalDataValidateTask(
                    requestId = requestId,
                    processId = processId,
                    createChangePersonalDataTaskCommand = capture(commandSlot),
                    assignee = currentUser
                )
            } returns newValidationTask

            // When
            val result = requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)

            // Then
            assertThat(result).isEqualTo(newValidationTask)

            // Verify command values
            with(commandSlot.captured) {
                assertThat(c9id).isEqualTo(c9Id)
                assertThat(c9Type).isEqualTo("400")
                assertThat(ssin).isEqualTo(ssin)
                assertThat(numbox).isEqualTo(numbox)
                assertThat(introductionDate).isEqualTo(introductionDate)
                assertThat(entityCode).isEqualTo(entityCode)
                assertThat(dossierId).isEqualTo("$sectOp-$opKey")
            }

            verifyOrder {
                currentUserPort.getCurrentUsername()
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, currentUser)
                waveTaskHelper.createChangePersonalDataValidateTask(
                    requestId = requestId,
                    processId = processId,
                    createChangePersonalDataTaskCommand = any<CreateChangePersonalDataTaskCommand>(),
                    assignee = currentUser
                )
            }
        }

        @Test
        fun `should return null when data capture task cannot be closed`() {
            // Given
            val taskCode = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE

            setupBaseMocks()
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithCitizenInfoAndModeOfPayment
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns mockRequestWithCitizenInfoAndModeOfPayment
            every { waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, currentUser) } returns false

            // When
            val result = requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)

            // Then
            assertThat(result).isNull()

            verify(exactly = 0) {
                waveTaskHelper.createChangePersonalDataValidateTask(any(), any(), any(), any())
            }
        }

        @Test
        fun `should load numbox from citizen port when stored numbox is null`() {
            // Given
            val taskCode = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
            val loadedNumbox = 9999
            val newValidationTask = WaveTask(
                processId = "new-process-123",
                taskId = "new-task-456",
                status = WaveTaskStatus.OPEN
            )
            
            // Create request with null numbox
            val requestWithNullNumbox = mockRequestWithCitizenInfoAndModeOfPayment.copy(numbox = null)

            setupBaseMocks()
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns loadedNumbox
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithCitizenInfoAndModeOfPayment
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns requestWithNullNumbox
            every { waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, currentUser) } returns true
            every {
                waveTaskHelper.createChangePersonalDataValidateTask(
                    requestId = requestId,
                    processId = processId,
                    createChangePersonalDataTaskCommand = capture(commandSlot),
                    assignee = currentUser
                )
            } returns newValidationTask

            // When
            val result = requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)

            // Then
            assertThat(result).isEqualTo(newValidationTask)

            // Verify the loaded numbox is used in the command
            with(commandSlot.captured) {
                assertThat(numbox).isEqualTo(loadedNumbox)
            }

            // Verify that getCitizenNumbox was called to load the missing numbox
            verify(exactly = 1) {
                loadCitizenPort.getCitizenNumbox(ssin)
            }
            
            verifyOrder {
                currentUserPort.getCurrentUsername()
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(any())
                loadCitizenPort.getCitizenNumbox(ssin)
                waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, currentUser)
                waveTaskHelper.createChangePersonalDataValidateTask(
                    requestId = requestId,
                    processId = processId,
                    createChangePersonalDataTaskCommand = any<CreateChangePersonalDataTaskCommand>(),
                    assignee = currentUser
                )
            }
        }

        @Test
        fun `should close validate data task and return null when successful`() {
            // Given
            val taskCode = WaveTaskPort.VALIDATION_DATA
            val correlationId = requestId.toString()

            setupBaseMocks()
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithUnionContribution
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns mockRequestWithCitizenInfoAndModeOfPayment
            every { waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, currentUser) } returns true
            every { updateCitizenPort.updateCitizenInformation(requestId, any<UpdateCitizenCommand>()) } returns Unit
            every { syncFollowUpPort.persistSyncFollowUpAsPending(requestId, correlationId) } returns Unit
            every { snapshotPort.makeBaremaSnapshotReadonly(requestId) } returns Unit
            every { snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.ONEM) } returns Unit
            every {
                snapshotPort.makeCitizenInformationSnapshotReadonly(
                    requestId,
                    ExternalSource.AUTHENTIC_SOURCES
                )
            } returns Unit

            // When
            val result = requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)

            // Then
            assertThat(result).isNull()

            verifyOrder {
                currentUserPort.getCurrentUsername()
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(12345)
                snapshotPort.makeBaremaSnapshotReadonly(requestId)
                snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.ONEM)
                snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.AUTHENTIC_SOURCES)
                syncFollowUpPort.persistSyncFollowUpAsPending(requestId, correlationId)
                updateCitizenPort.updateCitizenInformation(requestId, any<UpdateCitizenCommand>())
                waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, currentUser)
            }

            verify(exactly = 0) {
                waveTaskHelper.createChangePersonalDataValidateTask(any(), any(), any(), any())
            }
        }

        @Test
        fun `should load numbox from citizen port when stored numbox is null for validation task`() {
            // Given
            val taskCode = WaveTaskPort.VALIDATION_DATA
            val loadedNumbox = 7777
            val correlationId = requestId.toString()
            
            // Create request with null numbox
            val requestWithNullNumbox = mockRequestWithCitizenInfoAndModeOfPayment.copy(numbox = null)

            setupBaseMocks()
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns loadedNumbox
            every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithUnionContribution
            every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns requestWithNullNumbox
            every { waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, currentUser) } returns true
            every { updateCitizenPort.updateCitizenInformation(requestId, any<UpdateCitizenCommand>()) } returns Unit
            every { syncFollowUpPort.persistSyncFollowUpAsPending(requestId, correlationId) } returns Unit
            every { snapshotPort.makeBaremaSnapshotReadonly(requestId) } returns Unit
            every { snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.ONEM) } returns Unit
            every {
                snapshotPort.makeCitizenInformationSnapshotReadonly(
                    requestId,
                    ExternalSource.AUTHENTIC_SOURCES
                )
            } returns Unit

            // When
            val result = requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)

            // Then
            assertThat(result).isNull()

            // Verify that getCitizenNumbox was called to load the missing numbox
            verify(exactly = 1) {
                loadCitizenPort.getCitizenNumbox(ssin)
            }
            
            verifyOrder {
                currentUserPort.getCurrentUsername()
                persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                persistChangePersonalDataPort.getChangePersonalDataByC9Id(any())
                loadCitizenPort.getCitizenNumbox(ssin)
                snapshotPort.makeBaremaSnapshotReadonly(requestId)
                snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.ONEM)
                snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.AUTHENTIC_SOURCES)
                syncFollowUpPort.persistSyncFollowUpAsPending(requestId, correlationId)
                updateCitizenPort.updateCitizenInformation(requestId, any<UpdateCitizenCommand>())
                waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, currentUser)
            }
        }

        @ParameterizedTest
        @ValueSource(strings = [WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE, WaveTaskPort.VALIDATION_DATA])
        fun `should assign task to current user for both task types`(taskCode: String) {
            // Given
            setupBaseMocks()

            if (taskCode == WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) {
                every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithCitizenInfoAndModeOfPayment
                every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns mockRequestWithCitizenInfoAndModeOfPayment
                every { waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, currentUser) } returns true
                every {
                    waveTaskHelper.createChangePersonalDataValidateTask(
                        requestId,
                        processId,
                        any<CreateChangePersonalDataTaskCommand>(),
                        currentUser
                    )
                } returns mockTask
            } else {
                every { persistChangePersonalDataPort.getChangePersonalDataById(requestId) } returns mockRequestWithUnionContribution
                every { persistChangePersonalDataPort.getChangePersonalDataByC9Id(any()) } returns mockRequestWithCitizenInfoAndModeOfPayment
                every { waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, currentUser) } returns true
                every {
                    updateCitizenPort.updateCitizenInformation(
                        requestId,
                        any<UpdateCitizenCommand>()
                    )
                } returns Unit
                every { syncFollowUpPort.persistSyncFollowUpAsPending(requestId, requestId.toString()) } returns Unit
                every { snapshotPort.makeBaremaSnapshotReadonly(requestId) } returns Unit
                every {
                    snapshotPort.makeCitizenInformationSnapshotReadonly(
                        requestId,
                        ExternalSource.ONEM
                    )
                } returns Unit
                every {
                    snapshotPort.makeCitizenInformationSnapshotReadonly(
                        requestId,
                        ExternalSource.AUTHENTIC_SOURCES
                    )
                } returns Unit
            }

            // When
            requestManagementService.closeTaskForRequestAndCreateNext(requestId, taskCode)

            // Then
            verify(exactly = 1) {
                when (taskCode) {
                    WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE -> waveTaskHelper.assignAndSleepDataCaptureTask(
                        requestId,
                        taskId,
                        currentUser
                    )

                    WaveTaskPort.VALIDATION_DATA -> waveTaskHelper.assignAndSleepValidateDataTask(
                        requestId,
                        taskId,
                        currentUser
                    )
                }
            }
        }
    }

    @Nested
    inner class AssignTaskToUser {
        @Test
        fun `assignTaskToUser should get current username and delegate to waveTaskHelper`() {
            // Given
            val requestId = UUID.randomUUID()
            val currentUsername = "testUser"

            every { currentUserPort.getCurrentUsername() } returns currentUsername
            every { waveTaskHelper.assignTaskToUser(requestId, currentUsername) } returns Unit

            // When
            requestManagementService.assignTaskToUser(requestId)

            // Then
            verifyOrder {
                currentUserPort.getCurrentUsername()
                waveTaskHelper.assignTaskToUser(requestId, currentUsername)
            }
        }

        @Test
        fun `assignTaskToUser should handle empty username`() {
            // Given
            val requestId = UUID.randomUUID()
            val emptyUsername = ""

            every { currentUserPort.getCurrentUsername() } returns emptyUsername
            every { waveTaskHelper.assignTaskToUser(requestId, emptyUsername) } returns Unit

            // When
            requestManagementService.assignTaskToUser(requestId)

            // Then
            verify {
                waveTaskHelper.assignTaskToUser(requestId, emptyUsername)
            }
        }

        @Test
        fun `assignTaskToUser should propagate any exceptions from dependencies`() {
            // Given
            val requestId = UUID.randomUUID()
            val currentUsername = "testUser"
            val exception = RuntimeException("Test exception")

            every { currentUserPort.getCurrentUsername() } returns currentUsername
            every { waveTaskHelper.assignTaskToUser(requestId, currentUsername) } throws exception

            // When/Then
            org.assertj.core.api.Assertions.assertThatThrownBy {
                requestManagementService.assignTaskToUser(requestId)
            }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Test exception")

            verify {
                currentUserPort.getCurrentUsername()
                waveTaskHelper.assignTaskToUser(requestId, currentUsername)
            }
        }
    }
}