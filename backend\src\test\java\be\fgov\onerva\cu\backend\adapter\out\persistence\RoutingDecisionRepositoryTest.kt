package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RoutingDecisionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RoutingDecisionRepository
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import org.springframework.beans.factory.annotation.Autowired

@DataJpaTest
@SpringJUnitConfig
class RoutingDecisionRepositoryTest {

    @Autowired
    lateinit var routingDecisionRepository: RoutingDecisionRepository

    @Autowired
    lateinit var entityManager: TestEntityManager

    @Test
    fun `should find routing decisions by request information id`() {
        // given
        val changePersonalDataRequest = ChangePersonalDataRequestEntity(
            c9Id = 123L,
            c9Type = "C9_TYPE",
            opKey = "OP123",
            sectOp = "SECT01",
            requestDate = LocalDate.now(),
            ssin = "***********",
            documentType = IdentityDocumentType.ELECTRONIC,
            unemploymentOffice = null,
            paymentInstitution = null,
            introductionDate = LocalDate.now(),
            dateValid = null,
            scanNumber = null,
            scanUrl = null,
            operatorCode = null,
            entityCode = null,
            introductionType = null,
            dueDate = null,
            numbox = null,
            ec1Id = null,
            ec1DisplayUrl = null,
        )
        entityManager.persistAndFlush(changePersonalDataRequest)

        val requestInformation = RequestInformationEntity(
            request = changePersonalDataRequest,
            requestDate = LocalDate.now(),
            processedInWave = false
        )
        entityManager.persistAndFlush(requestInformation)

        val routingDecision1 = RoutingDecisionEntity(
            requestInformation = requestInformation,
            type = ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD,
            value = true,
            canBeProcessedInWave = false  // true != false (expected), so Wave processing not allowed
        )

        val routingDecision2 = RoutingDecisionEntity(
            requestInformation = requestInformation,
            type = ManualVerificationType.NON_BELGIAN_RESIDENT,
            value = false,
            canBeProcessedInWave = true   // false == false (expected), so Wave processing allowed
        )

        entityManager.persistAndFlush(routingDecision1)
        entityManager.persistAndFlush(routingDecision2)

        // when
        val result = routingDecisionRepository.findByRequestInformationId(requestInformation.id)

        // then
        assertThat(result).hasSize(2)
        assertThat(result.map { it.type }).containsExactlyInAnyOrder(
            ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD,
            ManualVerificationType.NON_BELGIAN_RESIDENT
        )
        assertThat(result.map { it.value }).containsExactlyInAnyOrder(true, false)
        assertThat(result.map { it.canBeProcessedInWave }).containsExactlyInAnyOrder(false, true)
    }

    @Test
    fun `should return empty list when no routing decisions found for request information id`() {
        // given  
        val nonExistentId = UUID.randomUUID()

        // when
        val result = routingDecisionRepository.findByRequestInformationId(nonExistentId)

        // then
        assertThat(result).isEmpty()
    }
}