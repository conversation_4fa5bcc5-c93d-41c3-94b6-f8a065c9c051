package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RoutingDecisionItemEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RoutingDecisionRepository
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.RoutingDecisionPort

@Component
@Transactional
class RoutingDecisionPersistenceAdapter(
    private val routingDecisionRepository: RoutingDecisionRepository,
    private val changePersonalDataRepository: ChangePersonalDataRepository,
) : RoutingDecisionPort {

    override fun getRoutingDecisions(requestId: UUID): Set<RoutingDecisionItem> {
        val routingDecisionEntities = routingDecisionRepository.findByRequestId(requestId)
        return routingDecisionEntities.map { entity ->
            RoutingDecisionItem(
                type = entity.type,
                value = entity.value
            )
        }.toSet()
    }

    override fun saveRoutingDecisions(requestId: UUID, routingDecisions: Set<RoutingDecisionItem>) {
        val requestEntity = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        // Delete existing routing decisions for this request
        routingDecisionRepository.deleteByRequestId(requestId)

        // Create new routing decision entities
        val routingDecisionEntities = routingDecisions.map { decision ->
            RoutingDecisionItemEntity(
                request = requestEntity,
                type = decision.type,
                value = decision.value
            )
        }

        // Save new routing decisions
        routingDecisionRepository.saveAll(routingDecisionEntities)
    }

    override fun deleteRoutingDecisions(requestId: UUID) {
        routingDecisionRepository.deleteByRequestId(requestId)
    }
}