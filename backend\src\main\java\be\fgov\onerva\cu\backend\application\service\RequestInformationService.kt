package be.fgov.onerva.cu.backend.application.service

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.RoutingDecision
import be.fgov.onerva.cu.backend.application.exception.IncompleteRoutingDecisionsException
import be.fgov.onerva.cu.backend.application.exception.ProcessedInWaveImmutabilityException
import be.fgov.onerva.cu.backend.application.port.`in`.RequestInformationUseCase
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.common.utils.logger

@Service
@BusinessTransaction
class RequestInformationService(
    private val requestInformationPort: RequestInformationPort,
    private val changePersonalDataPort: PersistChangePersonalDataPort,
) : RequestInformationUseCase {
    private val log = logger

    override fun getRequestInformation(requestId: UUID): RequestInformation {
        return requestInformationPort.getRequestInformation(requestId)
    }

    override fun updateRequestInformation(requestId: UUID, newRequestInformation: RequestInformation) {
        val existingRequestInformation = try {
            requestInformationPort.getRequestInformation(requestId)
        } catch (e: Exception) {
            null
        }

        if (existingRequestInformation != null) {
            log.info("Updating request information for request ID: $requestId")
            
            // Apply business validation rules
            validateAllRoutingDecisionTypesPresent(newRequestInformation.routingDecisions)
            validateProcessedInWaveImmutability(requestId, existingRequestInformation, newRequestInformation.routingDecisions)
            
            requestInformationPort.updateRequestInformation(requestId, newRequestInformation)
        } else {
            log.info("Creating new request information for request ID: $requestId")
            
            // For new requests, still validate that all types are present
            validateAllRoutingDecisionTypesPresent(newRequestInformation.routingDecisions)
            
            requestInformationPort.persistRequestInformation(requestId, newRequestInformation)
        }
    }

    private fun validateAllRoutingDecisionTypesPresent(routingDecisions: Set<RoutingDecision>) {
        val allRequiredTypes = ManualVerificationType.entries.toSet()
        val providedTypes = routingDecisions.map { it.type }.toSet()
        
        if (providedTypes != allRequiredTypes) {
            val missingTypes = allRequiredTypes - providedTypes
            val extraTypes = providedTypes - allRequiredTypes
            
            val errorMessage = buildString {
                append("Routing decisions validation failed. ")
                if (missingTypes.isNotEmpty()) {
                    append("Missing types: ${missingTypes.joinToString(", ")}. ")
                }
                if (extraTypes.isNotEmpty()) {
                    append("Extra types: ${extraTypes.joinToString(", ")}. ")
                }
                append("All ManualVerificationType values must be provided exactly once.")
            }
            
            throw IncompleteRoutingDecisionsException(errorMessage)
        }
    }

    private fun validateProcessedInWaveImmutability(
        requestId: UUID,
        existingRequestInformation: RequestInformation, 
        newRoutingDecisions: Set<RoutingDecision>
    ) {
        // Get the basic request information to check if request is closed
        val changePersonalDataRequest = changePersonalDataPort.getChangePersonalDataById(requestId)
        val isRequestClosed = changePersonalDataRequest?.decisionType != null  // Indicates decision was made from mainframe
        val currentProcessedInWave = existingRequestInformation.processedInWave
        
        // Only apply immutability rule if:
        // 1. Request is closed (decision was made from mainframe)
        // 2. AND currently processedInWave is true
        if (isRequestClosed && currentProcessedInWave) {
            val wouldBeComputed = computeProcessedInWave(newRoutingDecisions)
            if (!wouldBeComputed) {
                throw ProcessedInWaveImmutabilityException(
                    "Cannot change processedInWave from true to false for a closed request. " +
                    "Once a closed request was decided to be processed in Wave, " +
                    "this status cannot be reverted to maintain historical consistency."
                )
            }
        }
    }

    private fun computeProcessedInWave(routingDecisions: Set<RoutingDecision>): Boolean {
        // If no routing decisions exist, cannot be processed in Wave
        if (routingDecisions.isEmpty()) return false
        
        // Get all manual verification types that need to be answered
        val allRequiredTypes = ManualVerificationType.entries.toSet()
        val answeredTypes = routingDecisions.map { it.type }.toSet()
        
        // All types must be answered
        if (answeredTypes != allRequiredTypes) return false
        
        // All answers must match expected values for Wave processing
        return routingDecisions.all { it.canBeProcessedInWave }
    }
}