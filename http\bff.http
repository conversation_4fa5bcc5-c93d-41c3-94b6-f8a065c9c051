### Get Aggregate request - CHANGE_PERSONAL_DATA_CAPTURE
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/aggregate-requests/change-personal-data/CHANGE_PERSONAL_DATA_CAPTURE/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Aggregate request - VALIDATION_DATA
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/aggregate-requests/change-personal-data/VALIDATION_DATA/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Aggregate request - non existing
GET {{bff-url}}/bff/api/aggregate-requests/change-personal-data/CHANGE_PERSONAL_DATA_CAPTURE/bbe368f3-acac-4450-ba78-7f7f73a1883b
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get basic request information
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}

GET {{bff-url}}/bff/api/requests/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get employee information for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json


### Update via Aggregate request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/aggregate-requests/change-personal-data/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "citizenInformation": {
    "birthDate": "1975-06-09",
    "nationalityCode": 150,
    "address": {
      "countryCode": 150,
      "street": "Pasteur",
      "houseNumber": "37",
      "boxNumber": "B777",
      "zipCode": "1000",
      "city": "92250",
      "validFrom": "2025-09-01"
    }
  },
  "modeOfPayment": {
    "iban": "****************",
    "otherPersonName": "The other person name",
    "validFrom": "2025-10-01"
  },
  "unionContribution": {
    "authorized": true,
    "effectiveDate": "2025-01-01"
  },
  "basicInfo": {
    "requestDate": "2025-01-02"
  }
}

### Update via Aggregate request
PUT {{bff-url}}/bff/api/aggregate-requests/change-personal-data/bbe368f3-acac-4450-ba78-7f7f73a1883b
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "citizenInformation": {
    "source": "ONEM",
    "birthDate": "1975-06-09",
    "nationality": 150,
    "address": {
      "country": 150,
      "street": "Pasteur",
      "houseNumber": "37",
      "boxNumber": null,
      "zipCode": "1000",
      "city": "92250",
      "validFrom": "2025-09-01"
    }
  },
  "modeOfPayment": {
    "source": "ONEM",
    "belgianSEPABankAccount": "****************"
  },
  "unionContribution": {
    "source": "ONEM",
    "authorized": false,
    "effectiveDate": "2025-01-01"
  },
  "basicInfo": {
    "requestDate": "2025-01-02"
  }
}

### Update employee information for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "source": "ONEM",
  "birthDate": "1980-01-01",
  "nationalityCode": 150,
  "address": {
    "street": "Main Street",
    "city": "Springfield",
    "zipCode": "12345",
    "countryCode": 150,
    "houseNumber": "333",
    "boxNumber": "4444"
  }
}


### Get Lookup countries
GET {{bff-url}}/bff/api/lookup/country
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Lookup countries
GET {{bff-url}}/bff/api/lookup/country?searchQuery=Belgique
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Lookup countries
GET {{bff-url}}/bff/api/lookup/country?searchQuery=1
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json


### Get Lookup nationalities
GET {{bff-url}}/bff/api/lookup/nationality
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Lookup nationalities
GET {{bff-url}}/bff/api/lookup/nationality?searchQuery=Etats
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Lookup nationalities
GET {{bff-url}}/bff/api/lookup/nationality?searchQuery=150
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Lookup cities
GET {{bff-url}}/bff/api/lookup/city
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get Lookup cities
GET {{bff-url}}/bff/api/lookup/city?searchQuery=1390
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json


### Get Keycloak config
GET {{bff-url}}/bff/api/config/keycloak
Content-Type: application/json

### Update employee information for a request - select C1
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "birthDate",
      "source": "C1"
    },
    {
      "fieldName": "nationality",
      "source": "C1"
    },
    {
      "fieldName": "address",
      "source": "C1"
    }
  ]
}


### Update employee information for a request - select ONEM
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "birthDate",
      "source": "C1"
    },
    {
      "fieldName": "nationality",
      "source": "ONEM"
    },
    {
      "fieldName": "address",
      "source": "ONEM"
    }
  ]
}

### Update employee information for a request - select - AUTHENIC_SOURCES
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "birthDate",
      "source": "C1"
    },
    {
      "fieldName": "nationality",
      "source": "ONEM"
    },
    {
      "fieldName": "address",
      "source": "AUTHENTIC_SOURCES"
    }
  ]
}

### Update mode of payment for a request - select - C1
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/mode-of-payment/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "account",
      "source": "C1"
    },
    {
      "fieldName": "otherPersonName",
      "source": "C1"
    }
  ]
}

### Update mode of payment for a request - select
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/mode-of-payment/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "account",
      "source": "C1"
    },
    {
      "fieldName": "otherPersonName",
      "source": "ONEM"
    }
  ]
}


### Close the CHANGE_PERSONAL_DATA_CAPTURE task for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/CHANGE_PERSONAL_DATA_CAPTURE/close
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get the redirect URL for the C51 application
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/request/c51/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json


### Close the VALIDATION_DATA task for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/VALIDATION_DATA/close
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Open S24 session
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/request/s24/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
content-Type: application/json

### Get C51 link
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/request/c51/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
content-Type: application/json

