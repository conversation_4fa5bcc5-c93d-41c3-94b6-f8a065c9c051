/**
 * BFF Api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressNullable } from './addressNullable';
import { FieldSource } from './fieldSource';


export interface CitizenInformationDetailNullableResponse { 
    /**
     * The birth date of the employee (format YYYY-MM-DD)
     */
    birthDate?: string;
    /**
     * The nationality of the employee
     */
    nationalityCode?: number;
    address?: AddressNullable;
    /**
     * Sources for individual fields
     */
    fieldSources?: Array<FieldSource>;
}

