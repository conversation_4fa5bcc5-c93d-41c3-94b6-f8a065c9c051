package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.hibernate.envers.DefaultRevisionEntity
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.envers.repository.support.DefaultRevisionMetadata
import org.springframework.data.history.Revision
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestInformationRepository
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RequestInformationPersistenceAdapterTest {
    @MockK
    lateinit var requestInformationRepository: RequestInformationRepository

    @MockK
    lateinit var changePersonalDataRepository: ChangePersonalDataRepository

    @InjectMockKs
    lateinit var requestInformationPersistenceAdapter: RequestInformationPersistenceAdapter

    @Nested
    @DisplayName("Persist request information Tests")
    inner class PersistRequestInformation {
        private val requestId = UUID.randomUUID()

        @Test
        fun `should persist request information`() {
            // given
            val requestInfo = RequestInformation(
                requestDate = LocalDate.of(2020, 1, 1),
                routingDecisions = emptySet()
            )

            val changePersonalDataEntity = ChangePersonalDataRequestEntity(
                c9Id = 12345,
                c9Type = "400",
                ssin = "*********",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                entityCode = "123456",
                scanUrl = "http://example.com/scan1",
                scanNumber = 12345L,
                operatorCode = 123,
                dateValid = LocalDate.now().plusDays(20),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalDataEntity
            every { requestInformationRepository.save(any()) } returns RequestInformationEntity(
                request = changePersonalDataEntity,
                requestDate = requestInfo.requestDate
            )

            // when
            requestInformationPersistenceAdapter.persistRequestInformation(requestId, requestInfo)

            // then
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 1) {
                requestInformationRepository.save(match {
                    it.requestDate == requestInfo.requestDate
                })
            }
        }

        @Test
        fun `Given persisting RequestInformation when requestId not found then an error is thrown`() {
            // given
            val requestInfo = RequestInformation(
                requestDate = LocalDate.of(2020, 1, 1),
                routingDecisions = emptySet()
            )
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // when then
            assertThatThrownBy {
                requestInformationPersistenceAdapter.persistRequestInformation(requestId, requestInfo)
            }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }

        @Test
        fun `Given getting request information then request information is returned`() {
            // given
            val requestInformationEntity = RequestInformationEntity(
                request = ChangePersonalDataRequestEntity(
                    c9Id = 12345,
                    c9Type = "400",
                    ssin = "*********",
                    opKey = "OP123",
                    sectOp = "SO123",
                    documentType = IdentityDocumentType.PAPER,
                    requestDate = LocalDate.now(),
                    introductionDate = LocalDate.now(),
                    unemploymentOffice = 123,
                    paymentInstitution = 1,
                    introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                    dueDate = LocalDate.now().plusDays(12),
                    entityCode = "123456",
                    scanUrl = "http://example.com/scan1",
                    scanNumber = 12345L,
                    operatorCode = 123,
                    dateValid = LocalDate.now().plusDays(20),
                    numbox = 12,
                    ec1Id = 1234,
                    ec1DisplayUrl = "http://example.com/ec1",
                ),
                requestDate = LocalDate.now()
            )

            val requestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = emptySet()
            )

            every { requestInformationRepository.findByRequestId(requestId) } returns requestInformationEntity

            // when
            val result = requestInformationPersistenceAdapter.getRequestInformation(requestId)

            // then
            verify(exactly = 1) { requestInformationRepository.findByRequestId(requestId) }
            assertThat(result).isNotNull
            assertThat(result.requestDate).isEqualTo(requestInfo.requestDate)
        }

        @Test
        fun `Given getting request information when request id is wrong then an error is thrown`() {
            // given
            every { requestInformationRepository.findByRequestId(requestId) } returns null

            // when then
            assertThatThrownBy {
                requestInformationPersistenceAdapter.getRequestInformation(requestId)
            }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }

        @Test
        fun `Given updating request information then the request information is updated`() {
            // Given
            val requestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = emptySet()
            )

            val existingRequestEntity = RequestInformationEntity(
                request = ChangePersonalDataRequestEntity(
                    c9Id = 12345,
                    c9Type = "400",
                    ssin = "*********",
                    opKey = "OP123",
                    sectOp = "SO123",
                    documentType = IdentityDocumentType.PAPER,
                    requestDate = LocalDate.now(),
                    introductionDate = LocalDate.now(),
                    unemploymentOffice = 123,
                    paymentInstitution = 1,
                    introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                    dueDate = LocalDate.now().plusDays(12),
                    entityCode = "123456",
                    scanUrl = "http://example.com/scan1",
                    scanNumber = 12345L,
                    operatorCode = 123,
                    dateValid = LocalDate.now().plusDays(20),
                    numbox = 12,
                    ec1Id = 1234,
                    ec1DisplayUrl = "http://example.com/ec1",
                ),
                requestDate = LocalDate.now()
            )

            every { requestInformationRepository.findByRequestId(requestId) } returns existingRequestEntity
            every { requestInformationRepository.save(any()) } returnsArgument 0

            // When
            requestInformationPersistenceAdapter.updateRequestInformation(requestId, requestInfo)

            // Then
            verify(exactly = 1) { requestInformationRepository.findByRequestId(requestId) }
            verify(exactly = 1) { requestInformationRepository.save(any()) } // Ensure save() is called
        }

        @Test
        fun `Given updating request information when request id is wrong then an error is thrown`() {
            // Given
            every { requestInformationRepository.findByRequestId(requestId) } returns null

            // When Then
            assertThatThrownBy {
                requestInformationPersistenceAdapter.updateRequestInformation(
                    requestId,
                    RequestInformation(
                        requestDate = LocalDate.now(),
                        routingDecisions = emptySet()
                    )
                )
            }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }

        @Test
        fun `Given getLatestRevision should return the latest revision number`() {
            //Given
            val request = RequestInformationEntity(
                request = ChangePersonalDataRequestEntity(
                    c9Id = 12345,
                    c9Type = "400",
                    ssin = "*********",
                    opKey = "OP123",
                    sectOp = "SO123",
                    documentType = IdentityDocumentType.PAPER,
                    requestDate = LocalDate.now(),
                    introductionDate = LocalDate.now(),
                    unemploymentOffice = 123,
                    paymentInstitution = 1,
                    introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                    dueDate = LocalDate.now().plusDays(12),
                    entityCode = "123456",
                    scanUrl = "http://example.com/scan1",
                    scanNumber = 12345L,
                    operatorCode = 123,
                    dateValid = LocalDate.now().plusDays(20),
                    numbox = 12,
                    ec1Id = 1234,
                    ec1DisplayUrl = "http://example.com/ec1",
                ),
                requestDate = LocalDate.now()
            )
            every { requestInformationRepository.findByRequestId(requestId) } returns request
            every { requestInformationRepository.findLastChangeRevision(any()) } returns
                    Optional.of(Revision.of(DefaultRevisionMetadata(DefaultRevisionEntity()), request))
            //When
            requestInformationPersistenceAdapter.getLatestRevision(requestId)

            //Then
            verify(exactly = 1) { requestInformationRepository.findLastChangeRevision(any()) }
        }

        @Test
        fun `Given getLatestRevision when request id is wrong then an error is thrown`() {
            //Given
            every { requestInformationRepository.findByRequestId(requestId) } returns null

            //When Then
            assertThatThrownBy {
                requestInformationPersistenceAdapter.getLatestRevision(requestId)
            }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }
    }

    @Nested
    inner class GetEntityId {
        @Test
        fun `getEntityId should return the entity id when request exists`() {
            // Given
            val requestEntity = RequestInformationEntity(
                request = ChangePersonalDataRequestEntity(
                    c9Id = 12345,
                    c9Type = "400",
                    ssin = "*********",
                    opKey = "OP123",
                    sectOp = "SO123",
                    documentType = IdentityDocumentType.PAPER,
                    requestDate = LocalDate.now(),
                    introductionDate = LocalDate.now(),
                    unemploymentOffice = 123,
                    paymentInstitution = 1,
                    introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                    dueDate = LocalDate.now().plusDays(12),
                    entityCode = "123456",
                    scanUrl = "http://example.com/scan1",
                    scanNumber = 12345L,
                    operatorCode = 123,
                    dateValid = LocalDate.now().plusDays(20),
                    numbox = 12,
                    ec1Id = 1234,
                    ec1DisplayUrl = "http://example.com/ec1",
                ),
                requestDate = LocalDate.now()
            )

            val requestId = requestEntity.id

            every { requestInformationRepository.findByRequestId(requestId) } returns requestEntity

            // When
            val result = requestInformationPersistenceAdapter.getEntityId(requestId)

            // Then
            assertThat(result).isEqualTo(requestId)
            verify(exactly = 1) { requestInformationRepository.findByRequestId(requestId) }
        }

        @Test
        fun `getEntityId should throw exception when request does not exist`() {
            // Given
            val requestId = UUID.randomUUID()

            every { requestInformationRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy {
                requestInformationPersistenceAdapter.getEntityId(requestId)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { requestInformationRepository.findByRequestId(requestId) }
        }
    }

    @Nested
    inner class GetRequestInformationForRevision {

        @Test
        fun `getRequestInformationForRevision should return domain object for valid revision`() {
            // Given
            val revision = 2

            val requestEntity = RequestInformationEntity(
                request = ChangePersonalDataRequestEntity(
                    c9Id = 12345,
                    c9Type = "400",
                    ssin = "*********",
                    opKey = "OP123",
                    sectOp = "SO123",
                    documentType = IdentityDocumentType.PAPER,
                    requestDate = LocalDate.now(),
                    introductionDate = LocalDate.now(),
                    unemploymentOffice = 123,
                    paymentInstitution = 1,
                    introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                    dueDate = LocalDate.now().plusDays(12),
                    entityCode = "123456",
                    scanUrl = "http://example.com/scan1",
                    scanNumber = 12345L,
                    operatorCode = 123,
                    dateValid = LocalDate.now().plusDays(20),
                    numbox = 12,
                    ec1Id = 1234,
                    ec1DisplayUrl = "http://example.com/ec1",
                ),
                requestDate = LocalDate.of(2020, 5, 15)
            )

            val requestId = requestEntity.id

            val revisionEntity = Revision.of(
                DefaultRevisionMetadata(DefaultRevisionEntity()),
                requestEntity
            )

            every { requestInformationRepository.findByRequestId(requestId) } returns requestEntity
            every { requestInformationRepository.findRevision(requestId, revision) } returns Optional.of(revisionEntity)

            // When
            val result = requestInformationPersistenceAdapter.getRequestInformationForRevision(requestId, revision)

            // Then
            assertThat(result).isNotNull
            assertThat(result.requestDate).isEqualTo(LocalDate.of(2020, 5, 15))

            verify(exactly = 1) { requestInformationRepository.findByRequestId(requestId) }
            verify(exactly = 1) { requestInformationRepository.findRevision(requestId, revision) }
        }

        @Test
        fun `getRequestInformationForRevision should throw exception when revision does not exist`() {
            // Given
            val revision = 3

            val requestEntity = RequestInformationEntity(
                request = ChangePersonalDataRequestEntity(
                    c9Id = 12345,
                    c9Type = "400",
                    ssin = "*********",
                    opKey = "OP123",
                    sectOp = "SO123",
                    documentType = IdentityDocumentType.PAPER,
                    requestDate = LocalDate.now(),
                    introductionDate = LocalDate.now(),
                    unemploymentOffice = 123,
                    paymentInstitution = 1,
                    introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                    dueDate = LocalDate.now().plusDays(12),
                    entityCode = "123456",
                    scanUrl = "http://example.com/scan1",
                    scanNumber = 12345L,
                    operatorCode = 123,
                    dateValid = LocalDate.now().plusDays(20),
                    numbox = 12,
                    ec1Id = 1234,
                    ec1DisplayUrl = "http://example.com/ec1",
                ),
                requestDate = LocalDate.now()
            )

            val requestId = requestEntity.id

            every { requestInformationRepository.findByRequestId(requestId) } returns requestEntity
            every { requestInformationRepository.findRevision(requestId, revision) } returns Optional.empty()

            // When/Then
            assertThatThrownBy {
                requestInformationPersistenceAdapter.getRequestInformationForRevision(requestId, revision)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Revision not found for request $requestId and revision $revision")

            verify(exactly = 1) { requestInformationRepository.findByRequestId(requestId) }
            verify(exactly = 1) { requestInformationRepository.findRevision(requestId, revision) }
        }
    }

    @Nested
    @DisplayName("Patch Current Data with Revision Tests")
    inner class PatchCurrentDataWithRevision {
        @Test
        fun `patchCurrentDataWithRevision should successfully patch current request information data with revision data`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2
            val entityId = UUID.randomUUID()

            val requestEntity = mockk<ChangePersonalDataRequestEntity>()

            val currentData = RequestInformationEntity(
                requestDate = LocalDate.of(2023, 1, 15),
                request = requestEntity,
            ).apply { id = entityId }

            val revisionData = RequestInformationEntity(
                requestDate = LocalDate.of(2024, 8, 30),
                request = requestEntity
            )

            val revisionEntity = mockk<Revision<Int, RequestInformationEntity>>()
            every { revisionEntity.entity } returns revisionData

            every { requestInformationRepository.findByRequestId(requestId) } returns currentData
            every { requestInformationRepository.findRevision(entityId, revision) } returns Optional.of(revisionEntity)
            every { requestInformationRepository.save(any()) } returns currentData

            // When
            requestInformationPersistenceAdapter.patchCurrentDataWithRevision(requestId, revision)

            // Then
            verify(exactly = 1) { requestInformationRepository.findByRequestId(requestId) }
            verify(exactly = 1) { requestInformationRepository.findRevision(entityId, revision) }
            verify(exactly = 1) { requestInformationRepository.save(currentData) }

            // Vérifier que les données ont été mises à jour
            assertThat(currentData.requestDate).isEqualTo(LocalDate.of(2024, 8, 30))
        }
    }
}