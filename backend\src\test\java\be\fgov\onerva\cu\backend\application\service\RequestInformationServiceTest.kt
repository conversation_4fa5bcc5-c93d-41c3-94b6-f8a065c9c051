package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.RoutingDecision
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.exception.IncompleteRoutingDecisionsException
import be.fgov.onerva.cu.backend.application.exception.ProcessedInWaveImmutabilityException
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RequestInformationServiceTest {

    @MockK
    lateinit var requestInformationPort: RequestInformationPort
    
    @MockK
    lateinit var changePersonalDataPort: PersistChangePersonalDataPort

    @InjectMockKs
    lateinit var service: RequestInformationService

    @Test
    fun `Given getRequestInformation should return RequestInformation`() {
        // Given
        val requestId = UUID.randomUUID()
        val expectedRequestInformation = RequestInformation(
            requestDate = LocalDate.now(),
            routingDecisions = emptySet()
        )

        every { requestInformationPort.getRequestInformation(requestId) } returns expectedRequestInformation

        // When
        val result = service.getRequestInformation(requestId)

        // Then
        assertThat(result).isNotNull
        assertThat(result).isEqualTo(expectedRequestInformation)
        verify(exactly = 1) { requestInformationPort.getRequestInformation(requestId) }
    }

    @Nested
    inner class UpdateRequestInformationTests {
        @Test
        fun `Given existing request information should update it`() {
            // Given
            val requestId = UUID.randomUUID()
            val existingRequestInformation = RequestInformation(
                requestDate = LocalDate.now().minusDays(1),
                routingDecisions = emptySet(),
                processedInWave = false
            )
            val newRequestInformation = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = emptySet(),
                processedInWave = false
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInformation
            every { requestInformationPort.updateRequestInformation(requestId, newRequestInformation) } returns Unit

            // When
            service.updateRequestInformation(requestId, newRequestInformation)

            // Then
            verify(exactly = 1) { requestInformationPort.getRequestInformation(requestId) }
            verify(exactly = 1) { requestInformationPort.updateRequestInformation(requestId, newRequestInformation) }
            verify(exactly = 0) { requestInformationPort.persistRequestInformation(any(), any()) }
        }

        @Test
        fun `Given no existing request information should create new one`() {
            // Given
            val requestId = UUID.randomUUID()
            val newRequestInformation = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = emptySet(),
                processedInWave = false
            )

            every { requestInformationPort.getRequestInformation(requestId) } throws RuntimeException("Not found")
            every { requestInformationPort.persistRequestInformation(requestId, newRequestInformation) } returns Unit

            // When
            service.updateRequestInformation(requestId, newRequestInformation)

            // Then
            verify(exactly = 1) { requestInformationPort.getRequestInformation(requestId) }
            verify(exactly = 0) { requestInformationPort.updateRequestInformation(any(), any()) }
            verify(exactly = 1) { requestInformationPort.persistRequestInformation(requestId, newRequestInformation) }
        }
    }

    @Nested
    inner class ValidationTests {
        private val requestId = UUID.randomUUID()
        
        private fun createAllRoutingDecisions(): Set<RoutingDecision> {
            return ManualVerificationType.entries.map { type ->
                RoutingDecision(type, false)
            }.toSet()
        }

        private fun createChangePersonalDataRequest(decisionType: DecisionType? = null): ChangePersonalDataRequest {
            return ChangePersonalDataRequest(
                id = UUID.randomUUID(),
                c9Type = "400",
                c9id = 123L,
                ssin = "***********",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                decisionType = decisionType,
                decisionBarema = null,
                numbox = 12,
                documentType = IdentityDocumentType.PAPER,
                paymentInstitution = 1,
                entityCode = "123456",
                dossierId = null,
                introductionDate = LocalDate.now(),
                scanUrl = "http://example.com/scan1",
                unemploymentOffice = 123,
                dateValid = LocalDate.now(),
                scanNumber = 12345L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null
            )
        }

        @Test
        fun `updateRequestInformation should succeed when all manual verification types are provided for new request`() {
            // Given
            val requestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = createAllRoutingDecisions()
            )

            every { requestInformationPort.getRequestInformation(requestId) } throws RuntimeException("Not found")
            every { requestInformationPort.persistRequestInformation(requestId, requestInfo) } returns Unit

            // When/Then - should not throw
            service.updateRequestInformation(requestId, requestInfo)

            verify(exactly = 1) { requestInformationPort.persistRequestInformation(requestId, requestInfo) }
        }

        @Test
        fun `updateRequestInformation should throw exception when routing decisions are missing for new request`() {
            // Given - missing some manual verification types
            val incompleteRoutingDecisions = setOf(
                RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
                RoutingDecision(ManualVerificationType.NON_BELGIAN_RESIDENT, false)
                // Missing other types
            )
            val requestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = incompleteRoutingDecisions
            )

            every { requestInformationPort.getRequestInformation(requestId) } throws RuntimeException("Not found")

            // When/Then
            assertThatThrownBy {
                service.updateRequestInformation(requestId, requestInfo)
            }
                .isInstanceOf(IncompleteRoutingDecisionsException::class.java)
                .hasMessageContaining("Routing decisions validation failed")
                .hasMessageContaining("Missing types:")
        }

        @Test
        fun `updateRequestInformation should succeed when all manual verification types are provided for existing request`() {
            // Given
            val existingRequestInfo = RequestInformation(
                requestDate = LocalDate.now().minusDays(1),
                routingDecisions = createAllRoutingDecisions(),
                processedInWave = false
            )
            val newRequestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = createAllRoutingDecisions()
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInfo
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns createChangePersonalDataRequest()
            every { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) } returns Unit

            // When/Then - should not throw
            service.updateRequestInformation(requestId, newRequestInfo)

            verify(exactly = 1) { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) }
        }

        @Test
        fun `updateRequestInformation should throw exception when routing decisions are missing for existing request`() {
            // Given
            val existingRequestInfo = RequestInformation(
                requestDate = LocalDate.now().minusDays(1),
                routingDecisions = createAllRoutingDecisions(),
                processedInWave = false
            )
            val incompleteRoutingDecisions = setOf(
                RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
                RoutingDecision(ManualVerificationType.NON_BELGIAN_RESIDENT, false)
                // Missing other types
            )
            val newRequestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = incompleteRoutingDecisions
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInfo

            // When/Then
            assertThatThrownBy {
                service.updateRequestInformation(requestId, newRequestInfo)
            }
                .isInstanceOf(IncompleteRoutingDecisionsException::class.java)
                .hasMessageContaining("Routing decisions validation failed")
                .hasMessageContaining("Missing types:")
        }

        @Test
        fun `updateRequestInformation should succeed when request is open and processedInWave changes from true to false`() {
            // Given - request is open (no decisionType)
            val existingRequestInfo = RequestInformation(
                requestDate = LocalDate.now().minusDays(1),
                routingDecisions = createAllRoutingDecisions(),
                processedInWave = true // Currently true
            )
            val blockingRoutingDecisions = createAllRoutingDecisions().map { decision ->
                if (decision.type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD) {
                    RoutingDecision(decision.type, true) // This blocks Wave processing
                } else {
                    decision
                }
            }.toSet()
            val newRequestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = blockingRoutingDecisions
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInfo
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns createChangePersonalDataRequest(decisionType = null) // Open request
            every { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) } returns Unit

            // When/Then - should not throw because request is open
            service.updateRequestInformation(requestId, newRequestInfo)

            verify(exactly = 1) { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) }
        }

        @Test
        fun `updateRequestInformation should throw exception when closed request processedInWave changes from true to false`() {
            // Given - request is closed (has decisionType) and processedInWave is true
            val existingRequestInfo = RequestInformation(
                requestDate = LocalDate.now().minusDays(1),
                routingDecisions = createAllRoutingDecisions(),
                processedInWave = true // Currently true
            )
            val blockingRoutingDecisions = createAllRoutingDecisions().map { decision ->
                if (decision.type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD) {
                    RoutingDecision(decision.type, true) // This blocks Wave processing
                } else {
                    decision
                }
            }.toSet()
            val newRequestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = blockingRoutingDecisions
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInfo
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns createChangePersonalDataRequest(decisionType = DecisionType.C2) // Closed request

            // When/Then
            assertThatThrownBy {
                service.updateRequestInformation(requestId, newRequestInfo)
            }
                .isInstanceOf(ProcessedInWaveImmutabilityException::class.java)
                .hasMessageContaining("Cannot change processedInWave from true to false for a closed request")
                .hasMessageContaining("Once a closed request was decided to be processed in Wave")
        }

        @Test
        fun `updateRequestInformation should succeed when closed request processedInWave stays true`() {
            // Given - request is closed and processedInWave is true
            val existingRequestInfo = RequestInformation(
                requestDate = LocalDate.now().minusDays(1),
                routingDecisions = createAllRoutingDecisions(),
                processedInWave = true // Currently true
            )
            val allowingRoutingDecisions = createAllRoutingDecisions() // All false, allows Wave processing
            val newRequestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = allowingRoutingDecisions
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInfo
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns createChangePersonalDataRequest(decisionType = DecisionType.C2) // Closed request
            every { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) } returns Unit

            // When/Then - should not throw because computed value would still be true
            service.updateRequestInformation(requestId, newRequestInfo)

            verify(exactly = 1) { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) }
        }

        @Test
        fun `updateRequestInformation should succeed when closed request processedInWave stays false`() {
            // Given - request is closed but processedInWave is false
            val existingRequestInfo = RequestInformation(
                requestDate = LocalDate.now().minusDays(1),
                routingDecisions = createAllRoutingDecisions(),
                processedInWave = false // Currently false
            )
            val blockingRoutingDecisions = createAllRoutingDecisions().map { decision ->
                if (decision.type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD) {
                    RoutingDecision(decision.type, true) // This blocks Wave processing
                } else {
                    decision
                }
            }.toSet()
            val newRequestInfo = RequestInformation(
                requestDate = LocalDate.now(),
                routingDecisions = blockingRoutingDecisions
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInfo
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns createChangePersonalDataRequest(decisionType = DecisionType.C2) // Closed request
            every { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) } returns Unit

            // When/Then - should not throw because processedInWave is currently false
            service.updateRequestInformation(requestId, newRequestInfo)

            verify(exactly = 1) { requestInformationPort.updateRequestInformation(requestId, newRequestInfo) }
        }
    }
}