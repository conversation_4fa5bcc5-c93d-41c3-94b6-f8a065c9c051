package be.fgov.onerva.cu.backend.application.domain

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class ManualVerificationTypeTest {

    @ParameterizedTest
    @EnumSource(ManualVerificationType::class)
    fun `all verification types should expect false for Wave processing`(verificationType: ManualVerificationType) {
        assertThat(verificationType.expectedValueForWaveProcessing).isFalse
    }

    @ParameterizedTest
    @EnumSource(ManualVerificationType::class)
    fun `should allow Wave processing when answer matches expected value`(verificationType: ManualVerificationType) {
        val expectedValue = verificationType.expectedValueForWaveProcessing
        
        assertThat(verificationType.allowsWaveProcessing(expectedValue)).isTrue
    }

    @ParameterizedTest
    @EnumSource(ManualVerificationType::class)
    fun `should not allow Wave processing when answer does not match expected value`(verificationType: ManualVerificationType) {
        val unexpectedValue = !verificationType.expectedValueForWaveProcessing
        
        assertThat(verificationType.allowsWaveProcessing(unexpectedValue)).isFalse
    }

    @Test
    fun `routing decision should compute canBeProcessedInWave based on verification type`() {
        // Given - answer matches expected value (false)
        val allowingDecision = RoutingDecision(
            type = ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD,
            value = false
        )

        // Given - answer does not match expected value (true instead of false)
        val blockingDecision = RoutingDecision(
            type = ManualVerificationType.NON_BELGIAN_RESIDENT,
            value = true
        )

        // Then
        assertThat(allowingDecision.canBeProcessedInWave).isTrue
        assertThat(blockingDecision.canBeProcessedInWave).isFalse
    }
}