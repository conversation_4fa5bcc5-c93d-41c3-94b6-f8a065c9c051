/**
 * BFF Api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface AddressNullable { 
    /**
     * The country of residence
     */
    countryCode?: number;
    /**
     * The street name
     */
    street?: string;
    /**
     * The house number
     */
    houseNumber?: string;
    /**
     * The box number (optional)
     */
    boxNumber?: string;
    /**
     * The postal/zip code
     */
    zipCode?: string;
    /**
     * The city name
     */
    city?: string;
    /**
     * The date from which the address is valid (format YYYY-MM-DD)
     */
    validFrom?: string;
}

