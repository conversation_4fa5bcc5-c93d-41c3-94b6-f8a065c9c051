package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.RequestStatus
import be.fgov.onerva.cu.backend.application.domain.RoutingDecision
import be.fgov.onerva.cu.backend.application.exception.IncompleteRoutingDecisionsException
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.backend.application.port.out.RoutingDecisionPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RoutingDecisionServiceTest {

    @MockK
    lateinit var routingDecisionPort: RoutingDecisionPort
    
    @MockK
    lateinit var changePersonalDataPort: PersistChangePersonalDataPort

    @InjectMockKs
    lateinit var service: RoutingDecisionService

    private val requestId = UUID.randomUUID()

    private fun createChangePersonalDataRequest(status: RequestStatus = RequestStatus.OPEN): ChangePersonalDataRequest {
        return ChangePersonalDataRequest(
            id = requestId,
            c9Type = "400",
            c9id = 123L,
            ssin = "***********",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            decisionType = null,
            decisionBarema = null,
            numbox = 12,
            documentType = IdentityDocumentType.PAPER,
            paymentInstitution = 1,
            entityCode = "123456",
            dossierId = null,
            introductionDate = LocalDate.now(),
            scanUrl = "http://example.com/scan1",
            unemploymentOffice = 123,
            dateValid = LocalDate.now(),
            scanNumber = 12345L,
            operatorCode = 123,
            introductionType = IntroductionType.INTRO_FIRST_DEMAND,
            dueDate = LocalDate.now().plusDays(12),
            ec1Id = 1234,
            ec1DisplayUrl = "http://example.com/ec1",
            citizenInformation = null,
            modeOfPayment = null,
            unionContribution = null,
            requestInformation = null,
            changePersonalDataCaptureWaveTask = null,
            changePersonalDataValidateWaveTask = null,
            status = status,
            processInWave = false,
            routingDecisions = emptySet()
        )
    }

    private fun createAllRoutingDecisions(value: Boolean? = false): Set<RoutingDecision> {
        return ManualVerificationType.entries.map { type ->
            RoutingDecision(type, value)
        }.toSet()
    }

    private fun createPartialRoutingDecisions(): Set<RoutingDecision> {
        return setOf(
            RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            RoutingDecision(ManualVerificationType.NON_BELGIAN_RESIDENT, null)
            // Missing other types
        )
    }

    @Nested
    inner class GetRoutingDecisionsTests {
        
        @Test
        fun `should return existing routing decisions when all types are present`() {
            // Given
            val existingDecisions = createAllRoutingDecisions(false)
            every { routingDecisionPort.getRoutingDecisions(requestId) } returns existingDecisions

            // When
            val result = service.getRoutingDecisions(requestId)

            // Then
            assertThat(result).hasSize(ManualVerificationType.entries.size)
            assertThat(result).isEqualTo(existingDecisions)
            verify(exactly = 1) { routingDecisionPort.getRoutingDecisions(requestId) }
        }

        @Test
        fun `should return mixed decisions when some exist and fill missing with null values`() {
            // Given
            val existingDecisions = createPartialRoutingDecisions()
            every { routingDecisionPort.getRoutingDecisions(requestId) } returns existingDecisions

            // When
            val result = service.getRoutingDecisions(requestId)

            // Then
            assertThat(result).hasSize(ManualVerificationType.entries.size)
            
            // Check that existing decisions are preserved
            val citizenOver65Decision = result.find { it.type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD }
            assertThat(citizenOver65Decision?.value).isEqualTo(false)
            
            val nonBelgianResidentDecision = result.find { it.type == ManualVerificationType.NON_BELGIAN_RESIDENT }
            assertThat(nonBelgianResidentDecision?.value).isNull()
            
            // Check that missing types have null values
            val portWorkerDecision = result.find { it.type == ManualVerificationType.RELEVANT_TO_PORT_WORKER }
            assertThat(portWorkerDecision?.value).isNull()
            
            verify(exactly = 1) { routingDecisionPort.getRoutingDecisions(requestId) }
        }

        @Test
        fun `should return all types with null values when no routing decisions exist`() {
            // Given
            every { routingDecisionPort.getRoutingDecisions(requestId) } returns emptySet()

            // When
            val result = service.getRoutingDecisions(requestId)

            // Then
            assertThat(result).hasSize(ManualVerificationType.entries.size)
            assertThat(result.all { it.value == null }).isTrue()
            
            // Check all types are present
            val presentTypes = result.map { it.type }.toSet()
            assertThat(presentTypes).isEqualTo(ManualVerificationType.entries.toSet())
            
            verify(exactly = 1) { routingDecisionPort.getRoutingDecisions(requestId) }
        }
    }

    @Nested
    inner class UpdateRoutingDecisionsTests {

        @Test
        fun `should successfully update routing decisions for open request`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val routingDecisions = createAllRoutingDecisions(false)
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request
            every { routingDecisionPort.saveRoutingDecisions(requestId, routingDecisions) } returns Unit

            // When
            service.updateRoutingDecisions(requestId, routingDecisions)

            // Then
            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 1) { routingDecisionPort.saveRoutingDecisions(requestId, routingDecisions) }
        }

        @Test
        fun `should throw exception when request not found`() {
            // Given
            val routingDecisions = createAllRoutingDecisions(false)
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns null

            // When/Then
            assertThatThrownBy {
                service.updateRoutingDecisions(requestId, routingDecisions)
            }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("Request not found: $requestId")

            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { routingDecisionPort.saveRoutingDecisions(any(), any()) }
        }

        @Test
        fun `should throw exception when request is closed`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.CLOSED)
            val routingDecisions = createAllRoutingDecisions(false)
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request

            // When/Then
            assertThatThrownBy {
                service.updateRoutingDecisions(requestId, routingDecisions)
            }
                .isInstanceOf(IllegalStateException::class.java)
                .hasMessageContaining("Cannot update routing decisions for a closed request: $requestId")

            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { routingDecisionPort.saveRoutingDecisions(any(), any()) }
        }

        @Test
        fun `should throw exception when routing decisions are incomplete - missing types`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val incompleteDecisions = createPartialRoutingDecisions()
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request

            // When/Then
            assertThatThrownBy {
                service.updateRoutingDecisions(requestId, incompleteDecisions)
            }
                .isInstanceOf(IncompleteRoutingDecisionsException::class.java)
                .hasMessageContaining("Routing decisions validation failed")
                .hasMessageContaining("Missing types:")

            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { routingDecisionPort.saveRoutingDecisions(any(), any()) }
        }

        @Test
        fun `should throw exception when routing decisions have extra types`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val routingDecisionsWithExtra = createAllRoutingDecisions(false).toMutableSet()
            // Add a duplicate
            routingDecisionsWithExtra.add(RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, true))
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request

            // When/Then
            assertThatThrownBy {
                service.updateRoutingDecisions(requestId, routingDecisionsWithExtra)
            }
                .isInstanceOf(IncompleteRoutingDecisionsException::class.java)
                .hasMessageContaining("Routing decisions validation failed")

            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { routingDecisionPort.saveRoutingDecisions(any(), any()) }
        }

        @Test
        fun `should accept routing decisions with null values`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val routingDecisionsWithNulls = ManualVerificationType.entries.map { type ->
                RoutingDecision(type, if (type == ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD) false else null)
            }.toSet()
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request
            every { routingDecisionPort.saveRoutingDecisions(requestId, routingDecisionsWithNulls) } returns Unit

            // When
            service.updateRoutingDecisions(requestId, routingDecisionsWithNulls)

            // Then
            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 1) { routingDecisionPort.saveRoutingDecisions(requestId, routingDecisionsWithNulls) }
        }

        @Test
        fun `should accept routing decisions with mixed true and false values`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val mixedRoutingDecisions = ManualVerificationType.entries.mapIndexed { index, type ->
                RoutingDecision(type, index % 2 == 0) // alternating true/false
            }.toSet()
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request
            every { routingDecisionPort.saveRoutingDecisions(requestId, mixedRoutingDecisions) } returns Unit

            // When
            service.updateRoutingDecisions(requestId, mixedRoutingDecisions)

            // Then
            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 1) { routingDecisionPort.saveRoutingDecisions(requestId, mixedRoutingDecisions) }
        }
    }
}