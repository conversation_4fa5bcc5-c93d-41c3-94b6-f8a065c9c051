package be.fgov.onerva.cu.backend.application.domain

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class RequestInformationTest {

    @Test
    fun `should return false when no routing decisions are made`() {
        val requestInformation = RequestInformation(
            requestDate = LocalDate.now(),
            routingDecisions = emptySet()
        )

        assertThat(requestInformation.canBeProcessedInWave()).isFalse()
        assertThat(requestInformation.getFinalWaveProcessingStatus()).isFalse()
    }

    @Test
    fun `should return true when all routing decisions allow Wave processing`() {
        val routingDecisions = setOf(
            RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            RoutingDecision(ManualVerificationType.RELEVANT_TO_PORT_WORKER, false),
            RoutingDecision(ManualVerificationType.NON_BELGIAN_RESIDENT, false),
            RoutingDecision(ManualVerificationType.TRANSFER_BETWEEN_OP_OR_OC, false),
            RoutingDecision(ManualVerificationType.REQUEST_FOR_ECONOMIC_REASON, false),
            RoutingDecision(ManualVerificationType.RELEVANT_TO_APPRENTICESHIP, false),
            RoutingDecision(ManualVerificationType.CASE_OF_IMPULSION, false)
        )

        val requestInformation = RequestInformation(
            requestDate = LocalDate.now(),
            routingDecisions = routingDecisions
        )

        assertThat(requestInformation.canBeProcessedInWave()).isTrue
        assertThat(requestInformation.getFinalWaveProcessingStatus()).isFalse // processedInWave defaults to false
    }

    @Test
    fun `should return false when any routing decision blocks Wave processing`() {
        val routingDecisions = setOf(
            RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            RoutingDecision(ManualVerificationType.RELEVANT_TO_PORT_WORKER, false),
            RoutingDecision(ManualVerificationType.NON_BELGIAN_RESIDENT, true), // blocks!
            RoutingDecision(ManualVerificationType.TRANSFER_BETWEEN_OP_OR_OC, false),
            RoutingDecision(ManualVerificationType.REQUEST_FOR_ECONOMIC_REASON, false),
            RoutingDecision(ManualVerificationType.RELEVANT_TO_APPRENTICESHIP, false),
            RoutingDecision(ManualVerificationType.CASE_OF_IMPULSION, false)
        )

        val requestInformation = RequestInformation(
            requestDate = LocalDate.now(),
            routingDecisions = routingDecisions
        )

        assertThat(requestInformation.canBeProcessedInWave()).isFalse
        assertThat(requestInformation.getFinalWaveProcessingStatus()).isFalse
    }

    @Test
    fun `should use manually set processedInWave value`() {
        val routingDecisions = setOf(
            RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, true) // blocks
        )

        val requestInformation = RequestInformation(
            requestDate = LocalDate.now(),
            routingDecisions = routingDecisions,
            processedInWave = true // manually set to allow
        )

        assertThat(requestInformation.canBeProcessedInWave()).isFalse // computed says no
        assertThat(requestInformation.getFinalWaveProcessingStatus()).isTrue // manual value says yes
    }

    @Test
    fun `should use default false value when processedInWave not explicitly set`() {
        val routingDecisions = setOf(
            RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            RoutingDecision(ManualVerificationType.RELEVANT_TO_PORT_WORKER, false),
            RoutingDecision(ManualVerificationType.NON_BELGIAN_RESIDENT, false),
            RoutingDecision(ManualVerificationType.TRANSFER_BETWEEN_OP_OR_OC, false),
            RoutingDecision(ManualVerificationType.REQUEST_FOR_ECONOMIC_REASON, false),
            RoutingDecision(ManualVerificationType.RELEVANT_TO_APPRENTICESHIP, false),
            RoutingDecision(ManualVerificationType.CASE_OF_IMPULSION, false)
        )

        val requestInformation = RequestInformation(
            requestDate = LocalDate.now(),
            routingDecisions = routingDecisions // processedInWave defaults to false
        )

        assertThat(requestInformation.canBeProcessedInWave()).isTrue
        assertThat(requestInformation.getFinalWaveProcessingStatus()).isFalse // defaults to false
    }

    @Test
    fun `should return false when routing decisions are incomplete`() {
        val routingDecisions = setOf(
            RoutingDecision(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            RoutingDecision(ManualVerificationType.NON_BELGIAN_RESIDENT, false)
            // Missing other manual verification types
        )

        val requestInformation = RequestInformation(
            requestDate = LocalDate.now(),
            routingDecisions = routingDecisions
        )

        assertThat(requestInformation.canBeProcessedInWave()).isFalse
        assertThat(requestInformation.getFinalWaveProcessingStatus()).isFalse
    }
}