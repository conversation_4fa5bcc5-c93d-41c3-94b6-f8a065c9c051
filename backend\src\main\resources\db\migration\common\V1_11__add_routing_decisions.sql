-- Add routing decisions support and request status
-- This migration adds routing decisions linked to request and adds status field


-- Add status and process_in_wave columns to request (initially nullable)
alter table request
    add status varchar(10) null,
        process_in_wave bit not null default 0;
GO

-- Set status based on existing decision_from_mfx column
update request
set status = case
    when decision_from_mfx = 1 then 'CLOSED'
    else 'OPEN'
end;
GO

-- Make status NOT NULL after setting all values
alter table request
    alter column status varchar(10) not null;
GO

-- Create new routing_decision_item table linked directly to request
create table routing_decision_item
(
    id                          uniqueidentifier not null,
    request_id                  uniqueidentifier not null,
    type                        varchar(255) check (type in (
        'CITIZEN_OVER_65_YEARS_OLD',
        'RELEVANT_TO_PORT_WORKER',
        'NON_BELGIAN_RESIDENT',
        'TRANSFER_BETWEEN_OP_OR_OC',
        'REQUEST_FOR_ECONOMIC_REASON',
        'RELEVANT_TO_APPRENTICESHIP',
        'CASE_OF_IMPULSION'
    )) not null,
    value                       bit null,  -- nullable for unanswered questions
    created_by                  varchar(255),
    created_date                datetime2(6) not null default sysdatetime(),
    last_modified_by            varchar(255),
    last_modified_date          datetime2(6),
    primary key (id),
    constraint uq_routing_decision_item_request_type unique (request_id, type) -- prevent duplicate types per request
);
GO

-- Add foreign key constraint from routing_decision_item to request
alter table routing_decision_item
    add constraint fk_routing_decision_item_request
        foreign key (request_id)
            references request (id);
GO

-- Create index on request_id for better query performance
create nonclustered index idx_routing_decision_item_request_id
    on routing_decision_item (request_id);
GO

-- Create audit table for routing_decision_item
create table routing_decision_item_aud
(
    rev                         int not null,
    id                          uniqueidentifier not null,
    revtype                     smallint,
    request_id                  uniqueidentifier,
    type                        varchar(255),
    value                       bit,
    created_by                  varchar(255),
    created_date                datetime2(6),
    last_modified_by            varchar(255),
    last_modified_date          datetime2(6),
    primary key (rev, id)
);
GO

-- Add foreign key constraint from routing_decision_item_aud to revinfo
alter table routing_decision_item_aud
    add constraint fk_routing_decision_item_aud_revinfo
        foreign key (rev)
            references revinfo (rev);
GO

-- Update request_aud table to include the new columns
alter table request_aud
    add status varchar(10) null,
        process_in_wave bit null;
GO